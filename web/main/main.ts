// 🔥 首先加载环境变量配置
import * as dotenv from 'dotenv';
import * as path from 'path';

// 加载 .env.local 文件（优先级最高）- 从项目根目录加载
dotenv.config({ path: path.join(process.cwd(), '..', '.env.local') });
// 加载 .env 文件（备用）- 从项目根目录加载
dotenv.config({ path: path.join(process.cwd(), '..', '.env') });

import { app, BrowserWindow, ipcMain, dialog } from 'electron';
import * as fs from 'fs';
import { execSync, spawn } from 'child_process';
import * as XLSX from 'xlsx';
import * as localStorage from '../lib/local-storage';
import { Task, InsightReport, KeywordConfirmation } from '../types/task';
import { BrowserManager, Platform } from '../lib/browser-manager'; // Import Platform
import { chromium } from 'playwright'; // Import chromium

import { LoginRequiredError } from '../lib/platform-scraper'; // V2.0 引入登录错误类
import { AIConfig } from '../lib/unified-ai-system';
import { logger, logInfo, logError, logWarn } from '../lib/logger'; // V2.0 引入专业日志系统
import { APIConfigManager, apiConfigManager } from '../lib/api-config-manager'; // 🔥 新的统一配置管理器

import { unifiedAISystem } from '../lib/unified-ai-system'; // 🔥 统一AI系统
import { SimpleTaskExecutor } from '../lib/simple-task-executor'; // 🔥 V2.5 简化任务执行器
import { SimpleAIService } from '../lib/simple-ai-service'; // 🔥 V2.5 简化AI服务

// --- 配置管理 ---
// 🔥 移除硬编码的后台URL，改为环境变量
const ADMIN_BACKEND_URL = process.env.ADMIN_BACKEND_URL || 'https://your-admin-backend.com/api/ai-config';

/**
 * V3.0 进度类型定义
 */
interface V3TaskProgress {
  phase: string;
  message: string;
  current: number;
  total: number;
  mode?: string;
  timestamp?: string;
}

/**
 * 🎯 增强的类型适配器：将V3TaskProgress转换为Task进度更新，支持状态映射
 */
const adaptTaskProgressToTaskUpdate = (progress: V3TaskProgress): Partial<Task> => {
  // 🎯 使用映射表将后台阶段转换为UI状态
  const uiStatus = mapBackendPhaseToUIStatus(progress.phase);

  const update: Partial<Task> = {
    progress: {
      message: progress.message,
      total: progress.total,
      // 根据阶段映射不同的计数字段
      ...(progress.phase.includes('discover') && {
        discovered: progress.current,
        discovered_total: progress.total
      }),
      ...(progress.phase.includes('scrap') && {
        scraped: progress.current,
        scraped_total: progress.total
      })
    },
    updatedAt: new Date().toISOString()
  };

  // 🎯 只在状态确实发生变化时更新status字段
  // 避免覆盖终端状态（FAILED, COMPLETED, CANCELLED）
  const terminalStates = ['FAILED', 'COMPLETED', 'CANCELLED'];
  if (uiStatus && !terminalStates.includes(uiStatus)) {
    update.status = uiStatus as any;
  }

  return update;
};

/**
 * V3.0 任务结果类型定义
 */
interface V3TaskResult {
  success: boolean;
  status: string;
  data: {
    discoveredLinks?: string[];
    scrapedComments?: Record<string, string[]>;
    insights?: any[];
    recommendations?: any[];
    analysis?: any; // 🔥 V2.5 SimpleTaskExecutor支持
    metadata?: any;
  };
  metadata?: {
    timestamp: string;
    duration: number;
    tokensUsed?: number;
    toolCallsCount: number;
  };
  aiMode?: boolean;
  aiMetadata?: any;
}

/**
 * 🔧 类型适配器：将V3TaskResult转换为InsightReport，并判断任务是否真正成功
 * 🔥 V2.5更新：支持SimpleTaskExecutor的结果格式
 */
const adaptTaskResultToInsightReport = (result: V3TaskResult): { report: InsightReport; shouldMarkAsCompleted: boolean } => {
  // V3.0 AI+MCP系统结果处理
  if (result.aiMode && result.status === 'completed') {
    return {
      report: {
        summary: '✅ AI+MCP智能分析完成',
        demandKeywords: [], // 添加必需的 demandKeywords 字段
        insights: result.data?.insights || []
      } as InsightReport,
      shouldMarkAsCompleted: true
    };
  }

  // 🔥 V2.5 SimpleTaskExecutor结果处理
  if (!result.aiMode && result.status === 'completed') {
    const analysisData = result.data?.analysis || result.data?.insights;

    return {
      report: {
        summary: '✅ V2.5简化架构分析完成',
        demandKeywords: analysisData?.demandKeywords || [],
        insights: analysisData?.insights || []
      } as InsightReport,
      shouldMarkAsCompleted: true
    };
  }

  // 🔧 修复：添加空值检查，防止访问undefined属性
  const discoveredCount = result.data?.discoveredLinks?.length || 0;
  const scrapedCount = result.data?.scrapedComments ? Object.keys(result.data.scrapedComments).length : 0;

  // 🔧 判断任务是否真正成功：如果没有发现任何链接，应该标记为失败
  const shouldMarkAsCompleted = discoveredCount > 0 || scrapedCount > 0;

  const report: InsightReport = {
    summary: shouldMarkAsCompleted
      ? `任务完成。发现 ${discoveredCount} 个链接，爬取了 ${scrapedCount} 个页面的评论数据。`
      : `任务失败。未能发现任何有效链接，可能是网站结构变化或反爬虫限制。`,
    demandKeywords: [],
    insights: []
  };

  return { report, shouldMarkAsCompleted };
};

/**
 * 🔧 检查任务结果是否为暂停状态（V3.0不再需要此检查）
 */
const isTaskPaused = (result: any): boolean => {
  return false; // V3.0 AI+MCP系统不使用暂停机制
};

/**
 * 🔥 [已废弃] 初始化本地AI配置文件
 * 现在统一使用环境变量配置，不再创建.insight.config.json文件
 */
function initializeLocalAIConfig() {
  // 🔥 不再创建配置文件，改为检查环境变量
  if (process.env.NODE_ENV === 'development') {
    console.log('[Config] 🔥 Using unified environment variable configuration');
    console.log('[Config] Please ensure your .env file is properly configured');

    // 检查关键环境变量是否存在
    if (!process.env.AI_API_KEY) {
      console.warn('[Config] ⚠️ AI_API_KEY not found in environment variables');
      console.warn('[Config] Please copy .env.example to .env and configure your API settings');
    }
  }
}

/**
 * 🔥 从统一配置管理器读取AI配置（替代本地文件）
 */
async function readLocalAIConfig(): Promise<AIConfig> {
  try {
    // 🔥 使用统一配置管理器获取主要AI配置
    const providerConfig = apiConfigManager.getPrimaryAIConfig();

    // 转换为旧的AIConfig格式（向后兼容）
    const legacyConfig: AIConfig = {
      apiKey: providerConfig.apiKey,
      modelId: providerConfig.model,
      endpoint: `${providerConfig.baseURL}/chat/completions`,
      maxTokens: providerConfig.maxTokens,
      temperature: providerConfig.temperature
    };

    console.log(`[Config] 🔥 Using unified configuration manager (primary AI config)`);
    return legacyConfig;
  } catch (error) {
    console.error(`[Config] ❌ Failed to get AI configuration from unified manager:`, error);
    throw new Error('无法获取AI配置，请检查环境变量设置。请参考 .env.example 文件配置您的API信息。');
  }
}

/**
 * 从中央后台获取AI配置。
 */
async function fetchAIConfigFromBackend(): Promise<AIConfig> {
  try {
    // In a real scenario, you might add an authentication header
    // to identify the client, e.g., a license key.
    const response = await fetch(ADMIN_BACKEND_URL);
    if (!response.ok) {
      throw new Error(`Failed to fetch AI config from admin backend: ${response.statusText}`);
    }
    const config = await response.json();
    if (!config.apiKey || !config.modelId) {
      throw new Error('Invalid AI config received from admin backend.');
    }
    console.log('[Config] Successfully fetched AI config from admin backend.');
    return config;
  } catch (error) {
    console.error('[Config] CRITICAL: Could not fetch AI configuration.', error);
    // This error should be propagated to the task status.
    throw new Error('无法从主服务器获取AI配置，请联系管理员。');
  }
}

/**
 * 根据运行环境获取AI配置。
 */
async function getAIConfig(): Promise<AIConfig> {
  if (process.env.NODE_ENV === 'development') {
    return readLocalAIConfig();
  } else {
    return fetchAIConfigFromBackend();
  }
}

/**
 * 执行关键词聚类分析 - 支持V3.0和V2.5架构
 * @param keywords 关键词数组
 * @returns 聚类后的关键词主题数组
 */
async function performKeywordClustering(keywords: string[]): Promise<Array<{theme: string, keywords: string[]}>> {
  try {
    console.log(`[Main] Performing AI clustering for ${keywords.length} keywords...`);

    let clusteredResult: Array<{theme: string, keywords: string[]}>;

    // 优先使用V2.5简化架构
    if (simpleAIService) {
      console.log(`[Main] Using V2.5 SimpleAIService for keyword clustering`);

      const clusterResults = await simpleAIService.clusterKeywords(keywords);

      // 转换格式：移除selected字段，只保留theme和keywords
      clusteredResult = clusterResults.map(cluster => ({
        theme: cluster.theme,
        keywords: cluster.keywords
      }));

    } else {
      // 回退到V3.0统一AI系统
      console.log(`[Main] Falling back to V3.0 UnifiedAISystem for keyword clustering`);

      const result = await unifiedAISystem.clusterKeywords(keywords);

      if (!result.success) {
        throw new Error(result.error || 'Keyword clustering failed');
      }

      // 转换格式：移除selected字段，只保留theme和keywords
      clusteredResult = result.data.map(cluster => ({
        theme: cluster.theme,
        keywords: cluster.keywords
      }));
    }

    console.log(`[Main] AI clustering completed: ${clusteredResult.length} themes generated`);
    return clusteredResult;

  } catch (error) {
    console.error(`[Main] Keyword clustering failed:`, error);

    // 降级处理：返回原始关键词作为单个主题
    return [{
      theme: "用户输入关键词",
      keywords: keywords
    }];
  }
}

let mainWindow: BrowserWindow | null;

function createWindow() {
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      preload: path.join(__dirname, 'preload.js'),
      // Important: These two options are required for `require` to work in the renderer process
      // and to allow communication between main and renderer processes.
      nodeIntegration: false, // keep false for security
      contextIsolation: true, // keep true for security
    },
  });

  if (process.env.NODE_ENV === 'development') {
    mainWindow.loadURL('http://localhost:3000');
    // mainWindow.webContents.openDevTools(); // 注释掉，开发时不自动打开 DevTools
  } else {
    mainWindow.loadFile(path.join(__dirname, '../../renderer/out/index.html'));
  }

  mainWindow.on('closed', () => {
    mainWindow = null;
  });
}

// 🎯 浏览器安装器已移除 - 现在使用预置本地浏览器策略
// 本地浏览器路径: browsers/mac/Chromium.app - 无需下载安装

/**
 * 智能解析Excel或CSV文件，自动识别关键词列
 * 支持多种文件格式和列布局，能够准确识别关键词数据
 * @param filePath 文件的绝对路径
 * @returns 返回关键词数组的Promise
 */
async function parseFileForKeywords(filePath: string): Promise<string[]> {
  try {
    await fs.promises.access(filePath); // 检查文件是否存在和可访问
    const workbook = XLSX.readFile(filePath);
    const firstSheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[firstSheetName];

    // 转换为二维数组格式，保留原始数据结构
    const data: any[][] = XLSX.utils.sheet_to_json(worksheet, { header: 1, defval: "" });

    if (!data || data.length === 0) {
      throw new Error('文件内容为空或无法解析');
    }

    console.log(`[parseFileForKeywords] 📊 解析文件: ${data.length}行数据`);

    // 🔥 智能关键词列识别算法
    const keywordColumn = identifyKeywordColumn(data);
    console.log(`[parseFileForKeywords] 🎯 识别到关键词列: 第${keywordColumn + 1}列`);

    // 提取关键词数据
    const keywords = extractKeywordsFromColumn(data, keywordColumn);

    console.log(`[parseFileForKeywords] ✅ 成功提取${keywords.length}个关键词`);
    return keywords;

  } catch (error) {
    console.error(`[parseFileForKeywords] ❌ 文件解析失败:`, error);

    // 提供更友好的错误信息
    if (error && typeof error === 'object' && 'code' in error && error.code === 'ENOENT') {
      throw new Error(`文件未找到: ${filePath}`);
    }

    if (error instanceof Error) {
      throw new Error(`文件解析失败: ${error.message}`);
    }

    throw new Error('文件解析失败，请检查文件格式是否正确');
  }
}

/**
 * 🧠 智能识别关键词列算法
 * 通过多种启发式规则自动判断哪一列包含关键词数据
 */
function identifyKeywordColumn(data: any[][]): number {
  if (!data || data.length === 0) return 0;

  // 获取列数（取前几行的最大列数）
  const maxCols = Math.max(...data.slice(0, 5).map(row => row.length));
  if (maxCols === 0) return 0;

  console.log(`[identifyKeywordColumn] 🔍 分析${maxCols}列数据...`);

  const columnScores: number[] = new Array(maxCols).fill(0);

  // 分析前20行数据（避免处理过多数据影响性能）
  const sampleRows = data.slice(0, Math.min(20, data.length));

  for (let col = 0; col < maxCols; col++) {
    let score = 0;
    let validCellCount = 0;

    for (let row = 0; row < sampleRows.length; row++) {
      const cell = sampleRows[row][col];

      if (!cell || typeof cell !== 'string') continue;

      const cellValue = cell.toString().trim();
      if (!cellValue) continue;

      validCellCount++;

      // 🎯 评分规则1: 排除明显的序号列
      if (isSequentialNumber(cellValue, row)) {
        score -= 10; // 重度惩罚序号列
        continue;
      }

      // 🎯 评分规则2: 排除常见的表头词汇
      if (isHeaderWord(cellValue)) {
        score -= 5; // 惩罚表头词汇
        continue;
      }

      // 🎯 评分规则3: 奖励关键词特征
      score += scoreKeywordFeatures(cellValue);

      // 🎯 评分规则4: 奖励中文内容
      if (containsChinese(cellValue)) {
        score += 3;
      }

      // 🎯 评分规则5: 奖励合理长度
      if (cellValue.length >= 2 && cellValue.length <= 20) {
        score += 2;
      }
    }

    // 如果该列有效数据太少，降低分数
    if (validCellCount < sampleRows.length * 0.3) {
      score *= 0.5;
    }

    columnScores[col] = score;
    console.log(`[identifyKeywordColumn] 📊 第${col + 1}列得分: ${score.toFixed(1)} (有效数据: ${validCellCount})`);
  }

  // 找到得分最高的列
  const bestColumn = columnScores.indexOf(Math.max(...columnScores));
  console.log(`[identifyKeywordColumn] 🏆 最佳列: 第${bestColumn + 1}列 (得分: ${columnScores[bestColumn].toFixed(1)})`);

  return bestColumn;
}

/**
 * 判断是否为序号（连续数字）
 */
function isSequentialNumber(value: string, rowIndex: number): boolean {
  const num = parseInt(value);
  if (isNaN(num)) return false;

  // 检查是否为连续序号（允许从0或1开始）
  return num === rowIndex || num === rowIndex + 1;
}

/**
 * 判断是否为常见表头词汇
 */
function isHeaderWord(value: string): boolean {
  const headerWords = [
    '序号', '编号', 'id', 'ID', 'No', 'no', '号码',
    '关键词', '关键字', 'keyword', 'keywords', 'word', 'words',
    '产品', '商品', 'product', 'products', '名称', 'name',
    '类别', '分类', 'category', 'type', '标签', 'tag'
  ];

  return headerWords.some(header =>
    value.toLowerCase() === header.toLowerCase() ||
    value.includes(header) ||
    header.includes(value.toLowerCase())
  );
}

/**
 * 评估关键词特征得分
 */
function scoreKeywordFeatures(value: string): number {
  let score = 0;

  // 包含常见产品词汇
  const productWords = ['手机', '电脑', '衣服', '鞋子', '包包', '化妆品', '食品', '家具', '电器', '玩具'];
  if (productWords.some(word => value.includes(word))) {
    score += 5;
  }

  // 包含品牌或型号特征
  if (/[A-Za-z]+\d+|[A-Za-z]{2,}/.test(value)) {
    score += 2;
  }

  // 包含常见修饰词
  const modifiers = ['新款', '热销', '爆款', '推荐', '优质', '高端', '便宜', '实惠'];
  if (modifiers.some(mod => value.includes(mod))) {
    score += 3;
  }

  return score;
}

/**
 * 判断是否包含中文字符
 */
function containsChinese(value: string): boolean {
  return /[\u4e00-\u9fff]/.test(value);
}

/**
 * 从指定列提取关键词
 */
function extractKeywordsFromColumn(data: any[][], columnIndex: number): string[] {
  const keywords: string[] = [];
  const seenKeywords = new Set<string>(); // 用于去重

  for (let row = 0; row < data.length; row++) {
    const cell = data[row][columnIndex];

    if (!cell || typeof cell !== 'string') continue;

    const cellValue = cell.toString().trim();
    if (!cellValue) continue;

    // 跳过明显的表头和序号
    if (row === 0 && isHeaderWord(cellValue)) continue;
    if (isSequentialNumber(cellValue, row)) continue;

    // 处理可能包含多个关键词的单元格（用逗号、分号等分隔）
    const splitKeywords = cellValue.split(/[,，;；、\n\r\t]/)
      .map(kw => kw.trim())
      .filter(kw => kw.length > 0);

    for (const keyword of splitKeywords) {
      // 去重并过滤
      if (!seenKeywords.has(keyword) && isValidKeyword(keyword)) {
        seenKeywords.add(keyword);
        keywords.push(keyword);
      }
    }
  }

  return keywords;
}

/**
 * 验证关键词是否有效
 */
function isValidKeyword(keyword: string): boolean {
  // 长度检查
  if (keyword.length < 1 || keyword.length > 50) return false;

  // 排除纯数字
  if (/^\d+$/.test(keyword)) return false;

  // 排除纯符号
  if (/^[^\w\u4e00-\u9fff]+$/.test(keyword)) return false;

  return true;
}

// Function to send the updated task list to the renderer process
export const sendTasksToRenderer = async () => {
  if (mainWindow) {
    const tasks = await localStorage.getAllTasks();
    console.log(`[Main] Sending ${tasks.length} tasks to renderer`);
    mainWindow.webContents.send('tasks-updated', tasks);
    console.log(`[Main] tasks-updated event sent successfully`);
  } else {
    console.log(`[Main] Cannot send tasks to renderer: mainWindow is null`);
  }
};

// Function to send a single task update
export const sendTaskUpdate = async (taskId: string) => {
  try {
    const task = await localStorage.getTask(taskId);
    if (mainWindow && task) {
      console.log(`[Main] Sending task update for ${taskId} (id: ${task.id}, jobId: ${task.jobId}, status: ${task.status})`);
      // 发送给两个可能的监听器，确保无论使用id还是jobId都能收到
      mainWindow.webContents.send(`task-updated:${task.id}`, task);
      if (task.jobId !== task.id) {
        mainWindow.webContents.send(`task-updated:${task.jobId}`, task);
      }
    } else {
      // 🔧 修复：提供更详细的错误信息，帮助诊断问题
      if (!mainWindow) {
        console.warn(`[Main] Cannot send task update for ${taskId}: mainWindow is null`);
      } else if (!task) {
        console.warn(`[Main] Cannot send task update for ${taskId}: task not found (may be temporary file access issue)`);
        // 🔧 不要立即发送空任务列表，避免前端误认为任务被删除
      }
    }
  } catch (error) {
    console.error(`[Main] Error sending task update for ${taskId}:`, error);

    // 🔧 修复：检查是否为JSON损坏错误，如果是则触发清理
    const errorMessage = error instanceof Error ? error.message : String(error);
    if (errorMessage.includes('JSON') || errorMessage.includes('parse') || errorMessage.includes('Unexpected')) {
      console.warn(`[Main] 🗑️ Detected JSON corruption for task ${taskId}, triggering cleanup`);
      // 不阻塞当前流程，异步清理
      setTimeout(async () => {
        try {
          await localStorage.deleteTask(taskId, true); // 强制删除损坏的任务
          await sendTasksToRenderer(); // 刷新任务列表
          console.log(`[Main] ✅ Corrupted task ${taskId} cleaned up successfully`);
        } catch (cleanupError) {
          console.error(`[Main] ❌ Failed to cleanup corrupted task ${taskId}:`, cleanupError);
        }
      }, 100);
    }
  }
}

let browserManager: BrowserManager | null = null;

// 🔥 V2.5 简化架构组件
let simpleTaskExecutor: SimpleTaskExecutor | null = null;
let simpleAIService: SimpleAIService | null = null;

app.whenReady().then(async () => {
  // V2.0 首先初始化日志系统
  logger.initialize();
  logInfo('Application starting up', { component: 'Main' });

  // 🔥 初始化统一API配置管理器（优先级最高）
  try {
    // 🔧 修复：添加详细的环境变量诊断
    console.log('[Main] 🔍 Environment variables diagnostic:');
    console.log(`  - AI_API_KEY: ${process.env.AI_API_KEY ? '✅ Set' : '❌ Missing'}`);
    console.log(`  - AI_BASE_URL: ${process.env.AI_BASE_URL ? '✅ Set' : '❌ Missing'}`);
    console.log(`  - AI_MODEL: ${process.env.AI_MODEL ? '✅ Set' : '❌ Missing'}`);
    console.log(`  - VOLCENGINE_API_KEY: ${process.env.VOLCENGINE_API_KEY ? '✅ Set' : '❌ Missing'}`);
    console.log(`  - VOLCENGINE_BASE_URL: ${process.env.VOLCENGINE_BASE_URL ? '✅ Set' : '❌ Missing'}`);
    console.log(`  - VOLCENGINE_MODEL: ${process.env.VOLCENGINE_MODEL ? '✅ Set' : '❌ Missing'}`);
    console.log(`  - NODE_ENV: ${process.env.NODE_ENV}`);
    console.log(`  - Current working directory: ${process.cwd()}`);

    await apiConfigManager.initialize();
    logInfo('Unified API configuration manager initialized', { component: 'Main' });

    // ✅ API配置管理器已在上面初始化，包含基本的配置验证

    // 🔥 初始化统一AI系统
    try {
      await unifiedAISystem.initialize();
      logInfo('Unified AI system initialized successfully', { component: 'Main' });

      // 测试基本功能
      const systemStatus = unifiedAISystem.getStatus();
      logInfo('Unified AI system status', { component: 'Main', status: systemStatus });

    } catch (unifiedError) {
      logWarn('Unified AI system initialization failed', {
        component: 'Main',
        error: unifiedError instanceof Error ? unifiedError.message : String(unifiedError)
      });
    }

  } catch (error) {
    logError('Failed to initialize API configuration manager', {
      component: 'Main',
      error: error instanceof Error ? error.message : String(error)
    });

    // 🔧 修复：不要立即退出应用，而是降级处理
    console.error('[Main] ⚠️ API配置初始化失败，但继续启动应用以便用户查看错误信息');
    console.error('[Main] 请检查环境变量配置，确保.env.local文件存在且配置正确');

    // 创建一个最小配置，允许应用启动
    try {
      // 手动设置最小配置
      if (!process.env.AI_API_KEY && process.env.VOLCENGINE_API_KEY) {
        process.env.AI_API_KEY = process.env.VOLCENGINE_API_KEY;
      }
      if (!process.env.AI_BASE_URL && process.env.VOLCENGINE_BASE_URL) {
        process.env.AI_BASE_URL = process.env.VOLCENGINE_BASE_URL;
      }
      if (!process.env.AI_MODEL && process.env.VOLCENGINE_MODEL) {
        process.env.AI_MODEL = process.env.VOLCENGINE_MODEL;
      }

      // 重试初始化
      await apiConfigManager.initialize();
      console.log('[Main] ✅ API配置管理器重试初始化成功');
    } catch (retryError) {
      console.error('[Main] ❌ API配置管理器重试失败，应用将以受限模式运行');
      // 不抛出错误，允许应用继续启动
    }
  }

  initializeLocalAIConfig(); // 在应用准备好时初始化本地配置（向后兼容）

  // 🎯 本地浏览器已预置，无需安装检查 - 即开即用！
  logInfo('Local browser preset mode startup, skipping installation check', { component: 'Main' });

  createWindow();

  // 初始化全局实例
  const storagePath = app.getPath('userData');
  browserManager = new BrowserManager(storagePath);

  // V3.0 AI+MCP系统已移除，现在只使用V2.5简化架构

  // 🔥 V2.5 初始化简化架构组件（作为V3.0的补充或替代）
  try {
    logInfo('Initializing V2.5 simplified architecture components', { component: 'Main' });

    // 初始化SimpleAIService
    simpleAIService = new SimpleAIService();
    await simpleAIService.initialize();

    // 初始化SimpleTaskExecutor
    simpleTaskExecutor = new SimpleTaskExecutor(browserManager, simpleAIService, localStorage);
    await simpleTaskExecutor.initialize();

    logInfo('V2.5 simplified architecture initialized successfully', {
      component: 'Main',
      simpleAIService: 'initialized',
      simpleTaskExecutor: 'initialized'
    });

  } catch (error) {
    logError('Failed to initialize V2.5 simplified architecture', {
      component: 'Main',
      error: error instanceof Error ? error.message : String(error)
    });

    console.error('[Main] ❌ V2.5 简化架构初始化失败，详细错误:', error);
    console.error('[Main] 🔧 可能的解决方案:');
    console.error('[Main]   1. 检查SimpleAIService配置');
    console.error('[Main]   2. 确认BrowserManager正常运行');
    console.error('[Main]   3. 检查API配置是否正确');

    // 清空失败的实例
    simpleAIService = null;
    simpleTaskExecutor = null;
  }

  // 🔧 加载登录验证缓存
  loadVerificationCache();

  logInfo('Global instances initialized', {
    component: 'Main',
    storagePath,
    browserManager: 'initialized',
    simpleTaskExecutor: simpleTaskExecutor ? 'initialized' : 'failed',
    simpleAIService: simpleAIService ? 'initialized' : 'failed'
  });

  // ✅ 启用智能登录状态检查 - 现在使用headless模式，不会干扰用户
  setTimeout(() => {
    checkAllLoginStates().catch(error => {
      console.error('[Main] ❌ 启动时登录状态检查失败:', error);
    });
  }, 5000); // 延迟5秒，确保AI+MCP系统完全初始化

  app.on('activate', function () {
    if (BrowserWindow.getAllWindows().length === 0) createWindow();
  });
});



  // IPC handler to delete a task
  ipcMain.handle('delete-task', async (event, taskId, options: { force?: boolean } = {}) => {
    logInfo('Deleting task', { component: 'Main', taskId, force: options.force });
    try {
      // 先获取任务信息，用于后续的清理工作
      const taskToDelete = await localStorage.getTask(taskId);

      // 🔧 修复：如果任务正在执行且不是强制删除，先尝试停止任务
      if (!options.force && taskToDelete && ['PENDING', 'DISCOVERING', 'SCRAPING', 'ANALYZING'].includes(taskToDelete.status)) {
        console.log(`[Main] 🛑 Stopping active task ${taskId} before deletion`);

        // 先停止任务
        await updateTaskWithTracking(taskId, {
          status: 'CANCELLED',
          progress: { message: '任务已停止，准备删除...' }
        });

        // 等待一小段时间确保任务停止
        await new Promise(resolve => setTimeout(resolve, 500));
      }

      // 删除任务文件（如果是活跃任务，使用强制删除）
      const shouldForce = Boolean(options.force || (taskToDelete && ['PENDING', 'DISCOVERING', 'SCRAPING', 'ANALYZING'].includes(taskToDelete.status)));
      await localStorage.deleteTask(taskId, shouldForce);
      logInfo('Task deleted successfully', { component: 'Main', taskId });

      // 如果任务存在，清理相关的事件监听器
      if (taskToDelete) {
        // 发送任务删除事件给特定的监听器
        if (mainWindow) {
          mainWindow.webContents.send(`task-updated:${taskToDelete.id}`, null);
          if (taskToDelete.jobId !== taskToDelete.id) {
            mainWindow.webContents.send(`task-updated:${taskToDelete.jobId}`, null);
          }
        }
      }

      // 确保发送更新后的任务列表给前端
      await sendTasksToRenderer();
      logInfo('Sent updated tasks list to renderer after deletion', { component: 'Main', taskId });
    } catch (error) {
      logError('Failed to delete task', error, { component: 'Main', taskId });
      throw error;
    }
  });

  // IPC handler to confirm keywords
  ipcMain.handle('confirm-keywords', async (event, { taskId, keywords }) => {
    console.log(`Keywords confirmed for task ${taskId}`);
    
    let aiConfig: AIConfig;
    try {
      aiConfig = await getAIConfig();
    } catch (error) {
      const message = error instanceof Error ? error.message : "An unknown error occurred.";
      await updateTaskWithTracking(taskId, { status: 'FAILED', errorMessage: message });
      return;
    }

    const updatedTask = await updateTaskWithTracking(taskId, { finalKeywords: keywords, status: 'DISCOVERING' });

            // Run in background
        (async () => {
            if (!updatedTask) {
                console.error(`[Main] Task ${taskId} could not be found after updating keywords. Aborting resume.`);
                return;
            }

            let aiConfig: AIConfig;
            try {
                aiConfig = await getAIConfig();
            } catch (error) {
                const message = error instanceof Error ? error.message : "An unknown error occurred.";
                await updateTaskWithTracking(taskId, { status: 'FAILED', errorMessage: message });
                return;
            }

        try {
            // 🚀 使用可用的系统执行任务（V3.0 AI+MCP 或 V2.5 简化架构）
            const keywords = updatedTask.finalKeywords || updatedTask.initialKeywords || [];
            let result: any;

            if (simpleTaskExecutor && simpleAIService) {
              console.log(`[Main] Using V2.5 simplified architecture for confirmed keywords task ${taskId}`);

              simpleTaskExecutor.setProgressCallback((progress) => handleProgressUpdate(taskId, progress));

              const simpleResult = await simpleTaskExecutor.executeDeepScanWithConfirmedKeywords(keywords, {
                  maxResults: 50,
                  platforms: ['taobao', 'xiaohongshu']
              });

              result = {
                  success: simpleResult.success,
                  status: simpleResult.success ? 'completed' : 'failed',
                  data: simpleResult.data || {},
                  aiMode: false,
                  metadata: simpleResult.metadata ? {
                    ...simpleResult.metadata,
                    toolCallsCount: 0 // V2.5简化架构不使用工具调用
                  } : undefined
              };

            } else {
              throw new Error('Neither V3.0 AI+MCP system nor V2.5 simplified architecture is available. Please check configuration.');
            }

            // 🔧 修复：使用新的适配器，根据结果判断任务状态
            const { report, shouldMarkAsCompleted } = adaptTaskResultToInsightReport(result);
            if (shouldMarkAsCompleted) {
              await updateTaskWithTracking(taskId, { status: 'COMPLETED', reportData: report });
            } else {
              await updateTaskWithTracking(taskId, { status: 'FAILED', errorMessage: report.summary, reportData: report });
            }
        } catch (error) {
            console.error(`Scraping failed for task ${taskId}:`, error);
            const message = error instanceof Error ? error.message : "An unknown error occurred.";
            await updateTaskWithTracking(taskId, { status: 'FAILED', errorMessage: message });
        }
    })();

    return updatedTask;
  });

  // IPC handler to resume a paused task
  ipcMain.handle('resume-task', async (event, taskId: string) => {
    console.log(`[Main] Resuming task ${taskId}`);
    const task = await localStorage.getTask(taskId);

    if (!task) {
        console.error(`[Main] Cannot resume: Task ${taskId} not found.`);
        return;
    }

    // 扩展可恢复的状态类型
    const resumableStates = ['WAITING_USER_LOGIN', 'WAITING_CAPTCHA'];
    if (!resumableStates.includes(task.status)) {
        console.warn(`[Main] Task ${taskId} is not in a resumable state. Current status: ${task.status}`);
        return;
    }

    // Run resuming in the background
    (async () => {
        let aiConfig: AIConfig;
        try {
            aiConfig = await getAIConfig();
        } catch (error) {
            const message = error instanceof Error ? error.message : "An unknown error occurred.";
            await updateTaskWithTracking(taskId, { status: 'FAILED', errorMessage: message });
            return;
        }

        if (task) {
            try {
                // Clear paused links before resuming
                await updateTaskWithTracking(taskId, { pausedLinks: [] });

                const keywords = task.finalKeywords || task.initialKeywords || [];
                let result: any;

                // 🚀 使用可用的系统恢复任务（V3.0 AI+MCP 或 V2.5 简化架构）
                if (simpleTaskExecutor && simpleAIService) {
                    console.log(`[Main] Using V2.5 simplified architecture to resume task ${taskId}`);

                    simpleTaskExecutor.setProgressCallback((progress) => handleProgressUpdate(taskId, progress));

                    const simpleResult = await simpleTaskExecutor.executeTask(keywords, {
                        skipAIPreprocessing: task.scanMode === 'quick',
                        maxResults: 50,
                        platforms: ['taobao', 'xiaohongshu']
                    });

                    result = {
                        success: simpleResult.success,
                        status: simpleResult.success ? 'completed' : 'failed',
                        data: simpleResult.data || {},
                        aiMode: false,
                        metadata: simpleResult.metadata ? {
                          ...simpleResult.metadata,
                          toolCallsCount: 0 // V2.5简化架构不使用工具调用
                        } : undefined
                    };

                } else {
                    throw new Error('Neither V3.0 AI+MCP system nor V2.5 simplified architecture is available. Please check configuration.');
                }

                // 🔧 修复：使用新的适配器，根据结果判断任务状态
                const { report, shouldMarkAsCompleted } = adaptTaskResultToInsightReport(result);
                if (shouldMarkAsCompleted) {
                  await updateTaskWithTracking(taskId, { status: 'COMPLETED', reportData: report });
                } else {
                  await updateTaskWithTracking(taskId, { status: 'FAILED', errorMessage: report.summary, reportData: report });
                }
            } catch (error) {
                console.error(`[Main] Resuming task ${taskId} failed:`, error);
                const message = error instanceof Error ? error.message : "An unknown error occurred.";
                await updateTaskWithTracking(taskId, { status: 'FAILED', errorMessage: message });
            }
        } else {
            console.error(`[Main] Task ${taskId} not found after update. Cannot resume.`);
            // No need to update task if it's null, it's already gone.
        }
    })();
  });

  // IPC handler to retry a failed task
  ipcMain.handle('retry-task', async (event, taskId: string) => {
    console.log(`[Main] Retrying task ${taskId}`);
    const task = await localStorage.getTask(taskId);

    if (!task) {
        console.error(`[Main] Cannot retry: Task ${taskId} not found.`);
        return;
    }

    if (task.status !== 'FAILED') {
        console.warn(`[Main] Task ${taskId} is not in a failed state. Current status: ${task.status}`);
        return;
    }

    // 重置任务状态，重新开始
    await updateTaskWithTracking(taskId, { 
      status: 'PENDING', 
      errorMessage: undefined,
      progress: undefined,
      pausedLinks: [],
      updatedAt: new Date().toISOString()
    });

    // Run retry in the background
    (async () => {
        let aiConfig: AIConfig;
        try {
            aiConfig = await getAIConfig();
        } catch (error) {
            const message = error instanceof Error ? error.message : "An unknown error occurred.";
            await updateTaskWithTracking(taskId, { status: 'FAILED', errorMessage: message });
            return;
        }

        const updatedTask = await localStorage.getTask(taskId);
        if (updatedTask) {
            try {
                const keywords = updatedTask.finalKeywords || updatedTask.initialKeywords || [];
                let result: any;

                // 🚀 使用可用的系统重试任务（V3.0 AI+MCP 或 V2.5 简化架构）
                if (simpleTaskExecutor && simpleAIService) {
                    console.log(`[Main] Using V2.5 simplified architecture to retry task ${taskId}`);

                    simpleTaskExecutor.setProgressCallback((progress) => handleProgressUpdate(taskId, progress));

                    const simpleResult = await simpleTaskExecutor.executeTask(keywords, {
                        skipAIPreprocessing: updatedTask.scanMode === 'quick',
                        maxResults: 50,
                        platforms: ['taobao', 'xiaohongshu']
                    });

                    result = {
                        success: simpleResult.success,
                        status: simpleResult.success ? 'completed' : 'failed',
                        data: simpleResult.data || {},
                        aiMode: false,
                        metadata: simpleResult.metadata ? {
                          ...simpleResult.metadata,
                          toolCallsCount: 0 // V2.5简化架构不使用工具调用
                        } : undefined
                    };

                } else {
                    throw new Error('Neither V3.0 AI+MCP system nor V2.5 simplified architecture is available. Please check configuration.');
                }

                // 🔧 修复：使用新的适配器，根据结果判断任务状态
                const { report, shouldMarkAsCompleted } = adaptTaskResultToInsightReport(result);
                if (shouldMarkAsCompleted) {
                  await updateTaskWithTracking(taskId, { status: 'COMPLETED', reportData: report });
                } else {
                  await updateTaskWithTracking(taskId, { status: 'FAILED', errorMessage: report.summary, reportData: report });
                }
            } catch (error) {
                console.error(`[Main] Retrying task ${taskId} failed:`, error);
                const message = error instanceof Error ? error.message : "An unknown error occurred.";
                await updateTaskWithTracking(taskId, { status: 'FAILED', errorMessage: message });
            }
        } else {
            console.error(`[Main] Task ${taskId} not found after update. Cannot retry.`);
        }
    })();
  });

  // IPC handler for starting a task
  ipcMain.handle('start-task', async (event, taskData: Omit<Task, 'id' | 'status' | 'createdAt' | 'updatedAt'>) => {
    try {
      // 🔥 让createTask统一处理ID生成，避免ID冲突
      const newTask = await localStorage.createTask(taskData);
      const taskId = newTask.id; // 使用createTask生成的ID

      console.log(`[Main] Created new task: ${taskId}, sending to renderer immediately`);

      // 🔧 修复：标记任务开始执行，防止应用意外退出
      trackTaskUpdate();

      // 🔧 修复：确保任务文件确实被创建并可读取
      const verifyTask = await localStorage.getTask(taskId);
      if (!verifyTask) {
        console.error(`[Main] ❌ Task verification failed: ${taskId} not found after creation`);
        throw new Error(`Task ${taskId} was not properly created`);
      }

      // 🔧 修复：立即发送新任务给前端，确保前端能立即看到任务
      await sendTasksToRenderer();

      // 🔧 修复：额外发送单个任务更新，确保TaskMonitor能立即接收到
      await sendTaskUpdate(taskId);

      // 🔧 修复：立即返回任务给前端，让前端可以立即跳转
      console.log(`[Main] ✅ Task ${taskId} created and sent to frontend, returning immediately`);

      // 🔧 修复：在后台异步启动任务执行，不阻塞前端跳转
      setImmediate(async () => {
        // 添加延迟，确保前端已经完成跳转和状态同步
        await new Promise(resolve => setTimeout(resolve, 800));

        console.log(`[Main] 🚀 Starting background execution for task ${taskId}`);

        // 后台任务执行逻辑
        (async () => {
        // 🔧 修复：再次验证任务存在性，防止并发删除
        const taskBeforeStart = await localStorage.getTask(taskId);
        if (!taskBeforeStart) {
          console.warn(`[Main] Task ${taskId} was deleted before execution started`);
          return;
        }

        let aiConfig: AIConfig;
        try {
          aiConfig = await getAIConfig();
        } catch (error) {
          const message = error instanceof Error ? error.message : "An unknown error occurred.";
          // 🔧 修复：在更新状态前检查任务是否仍然存在
          const taskForError = await localStorage.getTask(taskId);
          if (taskForError) {
            await updateTaskWithTracking(taskId, { status: 'FAILED', errorMessage: message });
          }
          finishTaskUpdate(); // 确保清理状态计数器
          return;
        }

      try {
        // 🔥 使用V2.5简化架构
        if (simpleTaskExecutor && simpleAIService) {
          console.log(`[Main] 🔥 Using V2.5 simplified architecture for task ${taskId}`);
        } else {
          throw new Error('V2.5 simplified architecture is not available. Please check configuration.');
        }

        // 🔥 PRD流程重构：根据扫描模式和可用系统执行不同的流程
        if (taskData.scanMode === 'quick') {
          // 快速扫描模式：跳过AI预处理，直接启动爬虫
          console.log(`[Main] 🚀 Quick scan mode: skipping AI preprocessing`);

          const keywords = taskData.initialKeywords || [];
          let result: any;

          if (simpleTaskExecutor && simpleAIService) {
            // 使用V2.5简化架构
            simpleTaskExecutor.setProgressCallback((progress) => handleProgressUpdate(taskId, progress));

            const simpleResult = await simpleTaskExecutor.executeTask(keywords, {
              skipAIPreprocessing: true, // 快速扫描模式
              maxResults: 50,
              platforms: ['taobao', 'xiaohongshu']
            });

            result = {
              success: simpleResult.success,
              status: simpleResult.success ? 'completed' : 'failed',
              data: simpleResult.data || {},
              aiMode: false,
              metadata: simpleResult.metadata ? {
                ...simpleResult.metadata,
                toolCallsCount: 0 // V2.5简化架构不使用工具调用
              } : undefined
            };
          }

          // 处理快速扫描结果
          if (result && !isTaskPaused(result)) {
            const { report, shouldMarkAsCompleted } = adaptTaskResultToInsightReport(result);
            if (shouldMarkAsCompleted) {
              await updateTaskWithTracking(taskId, { status: 'COMPLETED', reportData: report });
            } else {
              await updateTaskWithTracking(taskId, { status: 'FAILED', errorMessage: report.summary, reportData: report });
            }
          }

        } else {
          // 深度扫描模式：实现完整的关键词处理流程
          console.log(`[Main] 🔍 Deep scan mode: processing keywords with AI`);

          const initialKeywords = taskData.initialKeywords || [];
          let finalKeywords: string[] = [];

          // 步骤1: 关键词数量判断
          if (initialKeywords.length < 5) {
            console.log(`[Main] Keywords count < 5, fetching Taobao suggestions...`);

            // 🔥 获取淘宝下拉词扩展关键词
            try {
              console.log(`[Main] 🔍 关键词数量 < 5，开始获取淘宝下拉词扩展...`);

              const { createTaobaoSuggestionsService } = await import('../lib/taobao-suggestions');

              // 🚀 集成BrowserManager获取淘宝页面实例
              if (!browserManager?.isRunning()) {
                console.log(`[Main] 🚀 启动浏览器管理器以获取淘宝下拉词...`);
                await browserManager?.launch();
              }

              const taobaoPage = await browserManager?.getPage('taobao');
              const taobaoService = createTaobaoSuggestionsService(taobaoPage);

              // 获取淘宝下拉词建议
              const suggestions = await taobaoService.getSuggestions(initialKeywords);
              console.log(`[Main] ✅ 获取到 ${suggestions.length} 个淘宝下拉词建议`);

              // 合并原始关键词和下拉词建议
              const allKeywords = [...initialKeywords, ...suggestions];
              console.log(`[Main] 📊 合并后总关键词数量: ${allKeywords.length}`);

              // 清理淘宝服务资源
              await taobaoService.cleanup();

              // AI聚类
              const clusteredKeywords = await performKeywordClustering(allKeywords);

              // 等待用户确认
              await updateTaskWithTracking(taskId, {
                status: 'WAITING_CONFIRMATION',
                processedKeywords: clusteredKeywords,
                progress: { message: '请确认AI推荐的关键词' }
              });

              console.log(`[Main] Task ${taskId} waiting for user confirmation`);
              return; // 暂停执行，等待用户确认

            } catch (error) {
              console.error(`[Main] Failed to fetch Taobao suggestions:`, error);
              // 降级处理：直接使用原始关键词
              finalKeywords = initialKeywords;
            }
          } else {
            console.log(`[Main] Keywords count >= 5, performing direct AI clustering...`);

            // 直接AI聚类
            const clusteredKeywords = await performKeywordClustering(initialKeywords);

            // 等待用户确认
            await updateTaskWithTracking(taskId, {
              status: 'WAITING_CONFIRMATION',
              processedKeywords: clusteredKeywords,
              progress: { message: '请确认AI推荐的关键词' }
            });

            console.log(`[Main] Task ${taskId} waiting for user confirmation`);
            return; // 暂停执行，等待用户确认
          }
        }

        // 注意：深度扫描模式在此处应该已经return，不会执行到这里
        // 如果执行到这里，说明代码逻辑有问题
        console.error(`[Main] Unexpected code path in deep scan mode for task ${taskId}`);
        await updateTaskWithTracking(taskId, {
          status: 'FAILED',
          errorMessage: 'Deep scan mode logic error: unexpected execution path'
        });

      } catch (error) {
        console.error(`[Main] Task ${taskId} failed:`, error);

        // V2.0 检查是否为登录需求错误，自动触发登录流程
        if (error instanceof LoginRequiredError) {
          console.log(`[Main] 🔐 Auto-triggering login for ${error.platform} due to: ${error.reason}`);

          try {
            // 更新任务状态为等待登录
            await updateTaskWithTracking(taskId, {
              status: 'WAITING_USER_LOGIN',
              progress: {
                message: `检测到${error.platform}需要登录，正在启动登录流程...`
              }
            });

            // 自动启动登录会话
            const loginResult = await startLoginSession(error.platform);

            if (loginResult.success) {
              console.log(`[Main] ✅ Auto-login successful for ${error.platform}, resuming task...`);

              // 登录成功，恢复任务
              await updateTaskWithTracking(taskId, {
                status: 'PENDING',
                progress: {
                  message: `${error.platform}登录成功，正在恢复任务...`
                }
              });

              // 🔥 V2.5 重新启动任务，使用简化架构
              const updatedTask = await localStorage.getTask(taskId);
              if (updatedTask && simpleTaskExecutor) {
                console.log(`[Main] 🔄 Resuming task ${taskId} after successful login for ${error.platform} using V2.5 simplified architecture`);

                const keywords = updatedTask.finalKeywords || updatedTask.initialKeywords || [];
                simpleTaskExecutor.setProgressCallback((progress) => handleProgressUpdate(taskId, progress));

                const simpleResult = await simpleTaskExecutor.executeTask(keywords, {
                    skipAIPreprocessing: updatedTask.scanMode === 'quick',
                    maxResults: 50,
                    platforms: ['taobao', 'xiaohongshu']
                });

                const result = {
                    success: simpleResult.success,
                    status: simpleResult.success ? 'completed' : 'failed',
                    data: simpleResult.data || {},
                    aiMode: false,
                    metadata: simpleResult.metadata ? {
                      ...simpleResult.metadata,
                      toolCallsCount: 0 // V2.5简化架构不使用工具调用
                    } : undefined
                };

                if (!isTaskPaused(result)) {
                  const { report, shouldMarkAsCompleted } = adaptTaskResultToInsightReport(result);
                  if (shouldMarkAsCompleted) {
                    await updateTaskWithTracking(taskId, { status: 'COMPLETED', reportData: report });
                  } else {
                    await updateTaskWithTracking(taskId, { status: 'FAILED', errorMessage: report.summary, reportData: report });
                  }
                } else {
                  console.log(`[Main] 📝 Task ${taskId} paused again after resume, waiting for further user action`);
                }
              }
            } else {
              // 登录失败
              await updateTaskWithTracking(taskId, {
                status: 'FAILED',
                errorMessage: `${error.platform}登录失败：${loginResult.error}`,
                progress: { message: `${error.platform}登录失败，任务终止` }
              });
            }

          } catch (loginError) {
            console.error(`[Main] Auto-login failed for ${error.platform}:`, loginError);
            await updateTaskWithTracking(taskId, {
              status: 'FAILED',
              errorMessage: `自动登录失败：${loginError instanceof Error ? loginError.message : String(loginError)}`,
              progress: { message: `自动登录失败，任务终止` }
            });
          }

          return; // 登录流程处理完成，不执行下面的通用错误处理
        }

        let friendlyMessage = error instanceof Error ? error.message : "An unknown error occurred.";

        // 为浏览器相关错误提供用户友好的解决方案
        if (friendlyMessage.includes('浏览器组件未安装') || friendlyMessage.includes('Chromium executable not found') || friendlyMessage.includes('browserType.launch')) {
          friendlyMessage = `浏览器组件未安装。请在终端中运行以下命令安装：
1. 尝试：npx playwright install chromium
2. 如果失败，请手动下载并安装 Chrome 浏览器
3. 重启应用后重试

技术详情：${friendlyMessage}`;
        }

        await updateTaskWithTracking(taskId, {
          status: 'FAILED',
          errorMessage: friendlyMessage,
          progress: { message: `任务失败：${friendlyMessage}` }
        });
        } finally {
          // 🔧 修复：确保异常情况下也清理状态计数器
          finishTaskUpdate();
        }
        })(); // 结束后台任务执行的异步函数
      }); // 结束setImmediate

      console.log(`[Main] Created and returning task: ${taskId}`);
      return newTask; // 🔥 返回完整的Task对象而不是taskId字符串
    } catch (error) {
      console.error(`[Main] Failed to create task:`, error);
      finishTaskUpdate(); // 确保清理状态计数器
      throw error;
    }
  });



/**
 * V2.0 独立的登录会话启动函数
 * 可以被IPC处理器和自动登录流程调用
 */
async function startLoginSession(platform: Platform): Promise<{ success: boolean; message?: string; error?: string }> {
  console.log(`[Main] Starting guided login session for: ${platform}`);

  let loginWindow: any = null; // Using `any` to avoid playwright Browser type issues here
  try {
    // 1. Launch a new, visible, independent browser instance for login
    loginWindow = await chromium.launch({ headless: false });
    const context = await loginWindow.newContext();
    const page = await context.newPage();

    // 2. Navigate to the login page
    const loginUrl = platform === 'taobao'
      ? 'https://login.taobao.com/member/login.jhtml'
      : 'https://www.xiaohongshu.com/login';
    await page.goto(loginUrl);

    // 3. Poll to check if login is successful
    // Success is determined by the URL changing from the login page to the main site
    await new Promise<void>((resolve, reject) => {
      const interval = setInterval(async () => {
        if (page.isClosed()) {
          clearInterval(interval);
          reject(new Error('Login page was closed by the user.'));
          return;
        }
        try {
          const currentUrl = page.url();
          const isLoggedIn = platform === 'taobao'
            ? currentUrl.includes('www.taobao.com') || currentUrl.includes('i.taobao.com')
            : currentUrl.includes('www.xiaohongshu.com/explore');

          if (isLoggedIn) {
            console.log(`[Main] Login successful for ${platform}!`);

            // 4. Save the storageState upon success
            // This path must match the one used by BrowserManager
            const storagePath = path.join(app.getPath('userData'), `${platform}-auth.json`);
            await context.storageState({ path: storagePath });

            // 🚀 设置登录成功冷却期，避免立即验证
            LOGIN_SUCCESS_COOLDOWN.set(platform, Date.now());

            console.log(`[Main] ${platform} login state saved to ${storagePath}`);
            clearInterval(interval);
            resolve();
          }
        } catch (error) {
          // Page might have closed during the check
          clearInterval(interval);
          reject(error);
        }
      }, 2000); // Check every 2 seconds

      // Timeout mechanism
      setTimeout(() => {
        clearInterval(interval);
        reject(new Error('Login session timed out after 5 minutes.'));
      }, 5 * 60 * 1000);
    });

    return { success: true, message: 'Login successful and session saved!' };

  } catch (error) {
    console.error(`[Main] Login session for ${platform} failed:`, error);
    return { success: false, error: (error as Error).message };
  } finally {
    // 5. Always close the login browser window
    if (loginWindow) {
      await loginWindow.close();
    }
  }
}

// IPC handler for guided login session (使用独立函数)
ipcMain.handle('start-login-session', async (_, platform: Platform) => {
  return await startLoginSession(platform);
});

// IPC handler for getting browser status
ipcMain.handle('get-browser-status', async () => {
  try {
    if (browserManager) {
      return {
        isRunning: browserManager.isRunning(),
        headless: browserManager.isHeadless(), // 需要添加这个方法
        hasActiveTasks: activeTaskUpdates > 0
      };
    } else {
      return {
        isRunning: false,
        headless: true,
        hasActiveTasks: false
      };
    }
  } catch (error) {
    console.error('[Main] 获取浏览器状态失败:', error);
    return {
      isRunning: false,
      headless: true,
      hasActiveTasks: false
    };
  }
});

// IPC handler for toggling browser visualization
ipcMain.handle('toggle-browser-visualization', async (_, headless: boolean) => {
  try {
    console.log(`[Main] Toggling browser visualization: ${headless ? 'headless' : 'visible'}`);

    if (browserManager) {
      const result = await browserManager.toggleVisualization(headless);

      // 如果任务被中断，发送通知到前端
      if (result.taskInterrupted && mainWindow) {
        mainWindow.webContents.send('browser-mode-changed', {
          headless,
          taskInterrupted: true,
          message: result.message
        });
      }

      return result;
    } else {
      throw new Error('BrowserManager not initialized');
    }
  } catch (error) {
    console.error('[Main] Failed to toggle browser visualization:', error);
    return {
      success: false,
      message: `浏览器模式切换失败: ${error instanceof Error ? error.message : 'Unknown error'}`,
      taskInterrupted: false
    };
  }
});

/**
 * 通过API验证平台登录状态
 */
// V3.0 AI+MCP登录状态同步函数已移除

// V3.0 syncPlatformLoginState函数已移除

/**
 * 🚀 准确探测账号是否失效 - 按照你的方案优化
 *
 * 核心特性：
 * 1. 秒级反馈：懒加载 Headless Playwright + 快速探测 URL
 * 2. 零误报：先检查文件 → 再访问"个人中心" URL → 解析 DOM/重定向
 * 3. 自动恢复：失效即删除旧状态 + 事件总线通知前端
 * 4. 可扩展：通用方法，后续接入京东、拼多多基本零改动
 */
async function verifyPlatformLoginStatus(platform: Platform): Promise<{ isValid: boolean; error?: string; lastCheck?: number }> {
  const startTime = Date.now();

  try {
    // 确保浏览器管理器已初始化
    if (!browserManager) {
      return { isValid: false, error: 'Browser manager not initialized' };
    }

    // 🚨 修复：检查浏览器是否已经启动，如果没有则尝试启动
    if (!browserManager.isRunning()) {
      try {
        console.log(`[Main] 🚀 懒加载启动浏览器进行 ${platform} 登录验证...`);
        await browserManager.launch();
      } catch (launchError) {
        console.error(`[Main] ❌ 浏览器启动失败:`, launchError);
        return {
          isValid: false,
          error: `Browser launch failed: ${(launchError as Error).message}`,
          lastCheck: Date.now()
        };
      }
    }

    // 🚨 修复：检查context是否已初始化
    try {
      // 创建临时页面进行验证
      const page = await browserManager.getPage(platform);

      try {
      // 根据平台访问特定的"个人中心" URL进行快速探测
      if (platform === 'taobao') {
        console.log(`[Main] 🔍 快速探测淘宝登录状态...`);

        // 🚀 修复：使用淘宝首页而非个人中心页面（避免反爬虫）
        await page.goto('https://www.taobao.com', {
          waitUntil: 'domcontentloaded',
          timeout: 10000
        });

        // 等待页面基本加载
        await page.waitForTimeout(2000);

        // 检查重定向：如果被重定向到登录页面说明未登录
        const currentUrl = page.url();
        if (currentUrl.includes('login.taobao.com') ||
            currentUrl.includes('passport.alibaba.com') ||
            currentUrl.includes('member1.taobao.com/member/login')) {
          return {
            isValid: false,
            error: 'Redirected to login page',
            lastCheck: Date.now()
          };
        }

        // 🚀 修复：使用实际测试验证的选择器
        const loginStatus = await page.evaluate(() => {
          // 检查用户名链接（最可靠的登录状态指示器）
          const userNameElement = document.querySelector('a[href*="my_itaobao"]');
          const userName = userNameElement ? userNameElement.textContent?.trim() : null;

          // 检查 .site-nav-user 元素
          const siteNavUser = document.querySelector('.site-nav-user');
          const siteNavUserText = siteNavUser ? siteNavUser.textContent?.trim() : null;

          return {
            userName,
            siteNavUserText,
            isLoggedIn: !!(userName && userName !== '' && userName !== '登录'),
            userNameElementExists: !!userNameElement,
            siteNavUserExists: !!siteNavUser
          };
        });

        if (!loginStatus.isLoggedIn) {
          return {
            isValid: false,
            error: `Login status check failed: userName="${loginStatus.userName}", siteNavUser="${loginStatus.siteNavUserText}"`,
            lastCheck: Date.now()
          };
        }

        const duration = Date.now() - startTime;
        console.log(`[Main] ✅ 淘宝登录状态验证成功 (${duration}ms, 用户: ${loginStatus.userName})`);
        return { isValid: true, lastCheck: Date.now() };

      } else if (platform === 'xiaohongshu') {
        console.log(`[Main] 🔍 快速探测小红书登录状态...`);

        // 🚀 优化：使用小红书首页进行验证，更稳定
        await page.goto('https://www.xiaohongshu.com/explore', {
          waitUntil: 'domcontentloaded',
          timeout: 10000
        });

        // 等待页面基本加载
        await page.waitForTimeout(2000);

        // 检查重定向：如果跳转到登录页面说明未登录
        const currentUrl = page.url();
        if (currentUrl.includes('/signin') ||
            currentUrl.includes('/login') ||
            currentUrl.includes('passport.xiaohongshu.com')) {
          return {
            isValid: false,
            error: 'Redirected to login page',
            lastCheck: Date.now()
          };
        }

        // 🔧 修复：更精确的小红书登录状态检测，避免误判
        const loginStatus = await page.evaluate(() => {
          // 🔧 修复：检查明确的登录按钮（使用原生DOM API）
          const loginButtons = document.querySelectorAll(
            'button, .login-btn, .signin-btn, .sign-in-btn, ' +
            '[data-testid*="login"], [data-testid*="signin"], ' +
            'a[href*="/signin"], a[href*="/login"]'
          );
          const loginButtonTexts = Array.from(loginButtons).map(btn => btn.textContent?.trim()).filter(Boolean);
          const hasExplicitLoginButton = loginButtonTexts.some(text =>
            text === '登录' || text === '注册' || text === 'Sign In' || text === 'Login'
          );

          // 🔧 修复：检查当前用户的头像（右上角的用户头像，不是推荐用户）
          const currentUserAvatar = document.querySelector(
            '.header .avatar, .nav .avatar, .user-info .avatar, ' +
            '[class*="header"] [class*="avatar"], [class*="nav"] [class*="avatar"], ' +
            '.top-bar .avatar, .navbar .avatar'
          );

          // 🔧 修复：检查用户菜单或下拉框（已登录用户才有）
          const userMenu = document.querySelector(
            '.user-menu, .user-dropdown, [class*="user-menu"], [class*="user-dropdown"], ' +
            '.profile-menu, [class*="profile-menu"]'
          );

          // 🔧 修复：检查是否有"我的"相关链接（已登录用户才有）
          const myLinks = document.querySelectorAll('a[href*="/me"], a[href*="/my"]');
          // 额外检查包含"我的"文本的链接
          const allLinks = document.querySelectorAll('a');
          const myTextLinks = Array.from(allLinks).filter(link =>
            link.textContent?.trim() === '我的' || link.textContent?.trim() === 'My'
          );

          // 🔧 修复：检查页面是否显示登录表单
          const loginForm = document.querySelector(
            'form[class*="login"], form[class*="signin"], ' +
            '.login-form, .signin-form, .auth-form'
          );

          return {
            hasExplicitLoginButton,
            hasCurrentUserAvatar: !!currentUserAvatar,
            hasUserMenu: !!userMenu,
            hasMyLinks: myLinks.length > 0 || myTextLinks.length > 0,
            hasLoginForm: !!loginForm,
            loginButtonTexts: loginButtonTexts.slice(0, 3), // 调试信息
            debugInfo: {
              currentUserAvatar: currentUserAvatar?.className || 'none',
              userMenu: userMenu?.className || 'none',
              myLinksCount: myLinks.length,
              myTextLinksCount: myTextLinks.length
            }
          };
        });

        // 🔧 修复：更精确的登录状态判断逻辑

        // 1. 如果有明确的登录表单，说明需要登录
        if (loginStatus.hasLoginForm) {
          return {
            isValid: false,
            error: 'Login form detected on page',
            lastCheck: Date.now()
          };
        }

        // 2. 如果有明确的登录按钮，说明未登录
        if (loginStatus.hasExplicitLoginButton) {
          return {
            isValid: false,
            error: `Explicit login button found: [${loginStatus.loginButtonTexts.join(', ')}]`,
            lastCheck: Date.now()
          };
        }

        // 3. 如果没有当前用户头像且没有用户菜单且没有"我的"链接，说明未登录
        if (!loginStatus.hasCurrentUserAvatar && !loginStatus.hasUserMenu && !loginStatus.hasMyLinks) {
          return {
            isValid: false,
            error: 'No current user indicators found (no avatar, menu, or my-links)',
            lastCheck: Date.now()
          };
        }

        // 4. 如果有当前用户头像或用户菜单或"我的"链接，说明已登录
        const duration = Date.now() - startTime;
        console.log(`[Main] ✅ 小红书登录状态验证成功 (${duration}ms, 头像: ${loginStatus.hasCurrentUserAvatar}, 菜单: ${loginStatus.hasUserMenu}, 我的链接: ${loginStatus.hasMyLinks})`);
        console.log(`[Main] 🔍 小红书登录检测详情:`, loginStatus.debugInfo);
        return { isValid: true, lastCheck: Date.now() };
      }

      return { isValid: false, error: 'Unknown platform', lastCheck: Date.now() };

      } finally {
        // 关闭临时页面
        await browserManager.closePage(platform, page);
      }

    } catch (contextError) {
      // 🚨 修复：context初始化错误的特殊处理
      const errorMessage = (contextError as Error).message;
      if (errorMessage.includes('context not initialized')) {
        console.log(`[Main] 📋 ${platform} context未初始化，无法验证登录状态`);
        return {
          isValid: false,
          error: `${platform} context not initialized`,
          lastCheck: Date.now()
        };
      }
      throw contextError; // 其他错误继续抛出
    }

  } catch (error) {
    const duration = Date.now() - startTime;
    console.error(`[Main] ❌ ${platform} 登录状态验证失败 (${duration}ms):`, error);
    return {
      isValid: false,
      error: (error as Error).message,
      lastCheck: Date.now()
    };
  }
}

/**
 * 🚀 短期会话缓存机制 - 持久化版本
 * 对短期会话（小红书 48h、淘宝 3-7d），将最后成功验证时间写入本地文件
 * <3h 不重复拉浏览器，提升性能，应用重启后仍然有效
 */
const LOGIN_VERIFICATION_CACHE = new Map<Platform, { lastVerified: number; isValid: boolean }>();
const CACHE_DURATION = 3 * 60 * 60 * 1000; // 🔧 修复：改为3小时缓存，更合理
const CACHE_FILE_PATH = path.join(app.getPath('userData'), 'login-verification-cache.json');

/**
 * 🚀 登录成功后的冷却期机制
 * 避免登录成功后立即验证导致的冲突
 */
const LOGIN_SUCCESS_COOLDOWN = new Map<Platform, number>();
const COOLDOWN_DURATION = 30 * 1000; // 30秒冷却期

/**
 * 🚨 全局检查锁机制
 * 避免多个组件同时触发检查导致浏览器实例疯狂创建
 */
let isCheckingLoginStatus = false;
const CHECK_LOCK_TIMEOUT = 10 * 1000; // 10秒超时

function shouldSkipVerification(platform: Platform): boolean {
  // 🚀 检查登录成功冷却期
  const loginSuccessTime = LOGIN_SUCCESS_COOLDOWN.get(platform);
  if (loginSuccessTime) {
    const now = Date.now();
    const timeSinceLogin = now - loginSuccessTime;

    if (timeSinceLogin < COOLDOWN_DURATION) {
      console.log(`[Main] 🔥 ${platform} 登录成功冷却期，跳过验证 (${Math.round(timeSinceLogin / 1000)}秒前登录)`);
      return true;
    } else {
      // 冷却期结束，清除记录
      LOGIN_SUCCESS_COOLDOWN.delete(platform);
    }
  }

  // 检查验证缓存
  const cached = LOGIN_VERIFICATION_CACHE.get(platform);
  if (!cached) {
    console.log(`[Main] 📋 ${platform} 无验证缓存，需要进行验证`);
    return false;
  }

  const now = Date.now();
  const timeSinceVerification = now - cached.lastVerified;
  const isWithinCacheWindow = timeSinceVerification < CACHE_DURATION;

  if (isWithinCacheWindow) {
    const minutesAgo = Math.round(timeSinceVerification / 1000 / 60);
    console.log(`[Main] 📋 ${platform} 登录状态使用缓存 (${minutesAgo}分钟前验证, 结果: ${cached.isValid ? '有效' : '无效'})`);

    // 🔧 修复：只有缓存结果为"有效"时才跳过验证
    // 如果缓存结果为"无效"，应该重新验证以确认当前状态
    if (cached.isValid) {
      return true; // 缓存显示有效，跳过验证
    } else {
      console.log(`[Main] 🔄 ${platform} 缓存显示无效，重新验证以确认当前状态`);
      return false; // 缓存显示无效，需要重新验证
    }
  } else {
    const minutesAgo = Math.round(timeSinceVerification / 1000 / 60);
    console.log(`[Main] 📋 ${platform} 验证缓存已过期 (${minutesAgo}分钟前验证)，需要重新验证`);
    return false;
  }
}

/**
 * 🔧 加载持久化缓存
 */
function loadVerificationCache(): void {
  try {
    if (fs.existsSync(CACHE_FILE_PATH)) {
      const cacheData = JSON.parse(fs.readFileSync(CACHE_FILE_PATH, 'utf-8'));
      let loadedCount = 0;

      for (const [platform, data] of Object.entries(cacheData)) {
        const cacheEntry = data as { lastVerified: number; isValid: boolean };

        // 🚀 修复：验证缓存数据的有效性
        if (cacheEntry && typeof cacheEntry.lastVerified === 'number' && typeof cacheEntry.isValid === 'boolean') {
          const now = Date.now();
          const age = now - cacheEntry.lastVerified;

          // 只加载未过期的缓存
          if (age < CACHE_DURATION) {
            LOGIN_VERIFICATION_CACHE.set(platform as Platform, cacheEntry);
            loadedCount++;
            console.log(`[Main] 📋 加载 ${platform} 缓存: ${Math.round(age / 1000 / 60)}分钟前验证, 结果: ${cacheEntry.isValid ? '有效' : '无效'}`);
          } else {
            console.log(`[Main] 📋 跳过 ${platform} 过期缓存: ${Math.round(age / 1000 / 60)}分钟前验证`);
          }
        }
      }

      console.log(`[Main] 📋 加载登录验证缓存完成: ${loadedCount}/${Object.keys(cacheData).length} 条有效记录`);
    } else {
      console.log(`[Main] 📋 登录验证缓存文件不存在，将创建新缓存`);
    }
  } catch (error) {
    console.error('[Main] ❌ 加载登录验证缓存失败:', error);
    // 清空缓存，避免使用损坏的数据
    LOGIN_VERIFICATION_CACHE.clear();
  }
}

/**
 * 🔧 保存持久化缓存
 */
function saveVerificationCache(): void {
  try {
    const cacheData = Object.fromEntries(LOGIN_VERIFICATION_CACHE);

    // 🚀 修复：确保目录存在
    const cacheDir = path.dirname(CACHE_FILE_PATH);
    if (!fs.existsSync(cacheDir)) {
      fs.mkdirSync(cacheDir, { recursive: true });
    }

    fs.writeFileSync(CACHE_FILE_PATH, JSON.stringify(cacheData, null, 2));
    console.log(`[Main] 💾 保存登录验证缓存: ${LOGIN_VERIFICATION_CACHE.size} 条记录到 ${CACHE_FILE_PATH}`);
  } catch (error) {
    console.error('[Main] ❌ 保存登录验证缓存失败:', error);
  }
}

function updateVerificationCache(platform: Platform, isValid: boolean): void {
  LOGIN_VERIFICATION_CACHE.set(platform, {
    lastVerified: Date.now(),
    isValid
  });
  // 🔧 立即保存到磁盘
  saveVerificationCache();
}

function getCachedVerification(platform: Platform): boolean | null {
  const cached = LOGIN_VERIFICATION_CACHE.get(platform);
  return cached ? cached.isValid : null;
}

/**
 * 🚀 启动时检查所有登录状态 - 按照你的方案实现
 *
 * 核心特性：
 * 1. 本地文件检查只需 30ms，先给 UI 一个"疑似已登录/未知"占位
 * 2. 后台验证后 patch UI，实现秒级反馈
 * 3. 短期会话缓存，<6h 不重复拉浏览器
 * 4. 事件总线通知前端实时刷新
 */
export async function checkAllLoginStates(): Promise<void> {
  console.log('[Main] 🚀 启动时检查所有登录状态...');

  const platforms: Platform[] = ['taobao', 'xiaohongshu'];

  // 第一阶段：快速文件检查，给前端占位状态
  const quickStatuses = platforms.map(platform => {
    const storageStatePath = path.join(app.getPath('userData'), `${platform}-auth.json`);
    const fileExists = fs.existsSync(storageStatePath);

    return {
      platform,
      isLoggedIn: fileExists,
      sessionValid: null, // 未知状态
      fileExists,
      status: 'checking' // 占位状态
    };
  });

  // 立即发送占位状态给前端
  if (mainWindow) {
    mainWindow.webContents.send('login-states-updated', quickStatuses);
  }

  // 第二阶段：后台API验证，逐个更新
  const results = await Promise.allSettled(
    platforms.map(platform => verifyPlatformLoginStatusWithCache(platform))
  );

  // 发送最终状态给前端
  const finalStatuses = results.map((result, index) => {
    const platform = platforms[index];

    if (result.status === 'fulfilled') {
      return result.value;
    } else {
      console.error(`[Main] ❌ ${platform} 状态检查失败:`, result.reason);
      return {
        platform,
        isLoggedIn: false,
        sessionValid: false,
        fileExists: false,
        error: result.reason?.message || 'Unknown error'
      };
    }
  });

  if (mainWindow) {
    mainWindow.webContents.send('login-states-updated', finalStatuses);
  }

  console.log('[Main] ✅ 所有登录状态检查完成');
}

/**
 * 带缓存的登录状态验证
 */
async function verifyPlatformLoginStatusWithCache(platform: Platform): Promise<any> {
  const storageStatePath = path.join(app.getPath('userData'), `${platform}-auth.json`);
  let isLoggedIn = false;
  let sessionValid = false;
  let fileExists = false;
  let lastLoginTime: string | undefined;

  try {
    if (fs.existsSync(storageStatePath)) {
      fileExists = true;
      const stats = fs.statSync(storageStatePath);
      const content = fs.readFileSync(storageStatePath, 'utf-8');
      const hasContent = content.trim().length > 100;
      isLoggedIn = hasContent;
      lastLoginTime = stats.mtime.toISOString();

      if (hasContent) {
        // 检查缓存，避免频繁验证
        if (shouldSkipVerification(platform)) {
          sessionValid = getCachedVerification(platform) || false;
        } else {
          // 🚨 修复：确保浏览器管理器就绪，如果需要则启动headless模式
          if (browserManager) {
            // 🎯 如果浏览器未运行，启动headless模式进行验证
            if (!browserManager.isRunning()) {
              try {
                console.log(`[Main] 🚀 启动headless模式浏览器进行 ${platform} 登录验证...`);
                await browserManager.launch(true, false); // headless=true, forTask=false
              } catch (launchError) {
                console.error(`[Main] ❌ 浏览器启动失败，跳过 ${platform} 验证:`, launchError);
                sessionValid = true; // 启动失败时假设有效，避免删除文件
                return { platform, isLoggedIn, sessionValid, fileExists, lastLoginTime };
              }
            }
            try {
              console.log(`[Main] 🔍 验证 ${platform} 登录状态...`);
              const verification = await verifyPlatformLoginStatus(platform);
              sessionValid = verification.isValid;

              // 更新缓存
              updateVerificationCache(platform, sessionValid);

              // 🚨 修复：只有在真正验证失败时才删除文件，而不是因为技术错误
              if (!sessionValid && !verification.error?.includes('context not initialized')) {
                console.log(`[Main] ❌ ${platform} 登录状态失效: ${verification.error}`);
                try {
                  fs.unlinkSync(storageStatePath);
                  console.log(`[Main] 🗑️ 已自动清除 ${platform} 的无效登录状态`);
                  isLoggedIn = false;
                  fileExists = false;
                } catch (cleanupError) {
                  console.error(`[Main] ❌ 清除 ${platform} 状态文件失败:`, cleanupError);
                }
              } else if (verification.error?.includes('context not initialized')) {
                // 🚨 修复：context未初始化时，假设登录状态有效，不删除文件
                console.log(`[Main] 📋 ${platform} context未初始化，跳过验证，保留登录状态文件`);
                sessionValid = true; // 假设有效，避免删除文件
              }
            } catch (verificationError) {
              console.log(`[Main] ⚠️ ${platform} 验证过程出错，保留登录状态文件:`, verificationError);
              sessionValid = true; // 出错时假设有效，避免删除文件
            }
          } else {
            // 🚨 修复：浏览器未就绪时，假设登录状态有效
            console.log(`[Main] 📋 ${platform} 浏览器未就绪，跳过验证，保留登录状态文件`);
            sessionValid = true;
          }
        }
      }
    }

    return {
      platform,
      isLoggedIn,
      lastLoginTime,
      sessionValid,
      fileExists
    };

  } catch (error) {
    console.error(`[Main] ❌ ${platform} 状态检查失败:`, error);
    return {
      platform,
      isLoggedIn: false,
      error: (error as Error).message
    };
  }
}

// V2.0 IPC handler for checking login status with API verification
ipcMain.handle('check-login-status', async () => {
  // 🚨 全局锁检查，避免重复调用
  if (isCheckingLoginStatus) {
    console.log('[Main] ⏳ 登录状态检查正在进行中，跳过重复调用');
    return [];
  }

  isCheckingLoginStatus = true;
  const lockTimeout = setTimeout(() => {
    console.log('[Main] ⚠️ 登录状态检查超时，释放锁');
    isCheckingLoginStatus = false;
  }, CHECK_LOCK_TIMEOUT);

  try {
    console.log('[Main] 🔄 手动检查登录状态...');

    const platforms: Platform[] = ['taobao', 'xiaohongshu'];
    const statuses = await Promise.allSettled(
      platforms.map(platform => verifyPlatformLoginStatusWithCache(platform))
    );

    const results = statuses.map((result, index) => {
      const platform = platforms[index];

      if (result.status === 'fulfilled') {
        return result.value;
      } else {
        return {
          platform,
          isLoggedIn: false,
          error: result.reason?.message || 'Unknown error'
        };
      }
    });

    console.log('[Main] ✅ 手动登录状态检查完成:', results);

    // 🔧 修复：发送登录状态更新事件给前端
    if (mainWindow) {
      mainWindow.webContents.send('login-states-updated', results);
    }

    return results;

  } finally {
    // 清除超时定时器并释放锁
    clearTimeout(lockTimeout);
    isCheckingLoginStatus = false;
  }
});

// V2.0 IPC handler for clearing login session
ipcMain.handle('clear-login-session', async (_, platform: Platform) => {
  console.log(`[Main] Clearing login session for: ${platform}`);

  try {
    // 🔧 修复路径不一致问题：使用与登录保存相同的路径
    const storageStatePath = path.join(app.getPath('userData'), `${platform}-auth.json`);

    if (fs.existsSync(storageStatePath)) {
      fs.unlinkSync(storageStatePath);
      console.log(`[Main] ✅ Cleared login session for ${platform}: ${storageStatePath}`);
    } else {
      console.log(`[Main] No login session file found for ${platform}: ${storageStatePath}`);
    }

    return { success: true, message: `${platform} login session cleared` };
  } catch (error) {
    console.error(`[Main] Failed to clear login session for ${platform}:`, error);
    return { success: false, error: (error as Error).message };
  }
});

// 智能进程退出机制 - 确保任务状态正确保存
let isQuitting = false;
let activeTaskUpdates = 0; // 跟踪正在进行的任务更新

// 注册任务更新计数器
const trackTaskUpdate = () => {
  activeTaskUpdates++;
};

const finishTaskUpdate = () => {
  activeTaskUpdates = Math.max(0, activeTaskUpdates - 1);
};

// 包装函数：跟踪所有任务状态更新
const updateTaskWithTracking = async (taskId: string, updates: Partial<Task>) => {
  trackTaskUpdate();
  try {
    console.log(`[Main] 🔧 正在更新任务 ${taskId}，状态变化:`, JSON.stringify(updates, null, 2));
    const result = await localStorage.updateTask(taskId, updates);

    if (!result) {
      console.error(`[Main] ❌ 任务更新失败，任务 ${taskId} 不存在或更新被拒绝`);
      return null;
    }

    console.log(`[Main] ✅ 任务 ${taskId} 更新成功，当前状态: ${result.status}`);

    // 🔧 修复：添加延迟，避免与浏览器重启过程冲突
    await new Promise(resolve => setTimeout(resolve, 50));

    await sendTaskUpdate(taskId);           // 单个任务更新
    await sendTasksToRenderer();            // ✅ 修复：确保前端收到任务状态变化
    return result;
  } catch (error) {
    console.error(`[Main] ❌ 任务 ${taskId} 更新过程中出错:`, error);

    // 🔧 修复：即使更新失败，也要尝试发送当前任务状态给前端
    try {
      await sendTaskUpdate(taskId);
      await sendTasksToRenderer();
    } catch (sendError) {
      console.error(`[Main] ❌ 发送任务状态失败:`, sendError);
    }

    throw error;
  } finally {
    finishTaskUpdate();
  }
};

// 🎯 V2.5进度回调适配器 - 使用标准化映射表
import {
  mapBackendPhaseToUIStatus,
  mapBackendPhaseToChinese,
  standardizeTaskProgress
} from '../lib/task-status-mapping';

const adaptV25ProgressToV3Progress = (progress: any): V3TaskProgress => {
  // 🎯 使用映射表标准化进度信息
  const standardized = standardizeTaskProgress({
    phase: progress.phase || 'unknown',
    message: progress.message,
    current: progress.current || 0,
    total: progress.total || 100,
    mode: progress.mode
  });

  return {
    phase: standardized.phase,
    message: standardized.chineseMessage,
    current: standardized.current,
    total: standardized.total,
    mode: standardized.mode || 'v2.5',
    timestamp: standardized.timestamp
  };
};

// 🎯 增强的进度更新处理函数 - 支持实时状态同步
const handleProgressUpdate = async (taskId: string, progress: V3TaskProgress | any) => {
  // 🔥 重要：检查当前任务状态，避免终端状态被覆盖
  const currentTask = await localStorage.getTask(taskId);
  if (currentTask && ['FAILED', 'COMPLETED', 'CANCELLED'].includes(currentTask.status)) {
    console.log(`[Main] ⚠️  忽略进度更新，任务 ${taskId} 已处于终端状态 ${currentTask.status}`);
    return;
  }

  // 🎯 适配V2.5进度格式到V3格式，使用标准化映射
  let v3Progress: V3TaskProgress;
  v3Progress = adaptV25ProgressToV3Progress(progress);
  console.log(`[Main] 📊 V2.5 progress standardized:`, {
    originalPhase: progress.phase,
    mappedPhase: v3Progress.phase,
    chineseMessage: v3Progress.message
  });

  // 🎯 使用增强的类型适配器转换TaskProgress为Partial<Task>
  const taskUpdate = adaptTaskProgressToTaskUpdate(v3Progress);

  // 🎯 立即更新任务状态，确保前端能实时看到变化
  await updateTaskWithTracking(taskId, taskUpdate);

  // 🎯 发送增强的任务进度事件到前端，包含映射后的状态信息
  if (mainWindow) {
    const enhancedProgressEvent = {
      taskId,
      message: v3Progress.message,
      phase: v3Progress.phase,
      uiStatus: mapBackendPhaseToUIStatus(progress.phase),
      chineseStatus: mapBackendPhaseToChinese(progress.phase),
      current: v3Progress.current,
      total: v3Progress.total,
      timestamp: v3Progress.timestamp
    };

    mainWindow.webContents.send('task-progress', enhancedProgressEvent);
    console.log(`[Main] 📡 Enhanced progress event sent:`, enhancedProgressEvent);
  }
};

// 智能退出函数 - 给任务状态更新留出时间
const gracefulExit = async () => {
  console.log('[Main] 🔄 开始优雅退出流程...');
  
  // 等待任务状态更新完成，最多等待3秒
  let waitTime = 0;
  const maxWaitTime = 3000;
  const checkInterval = 100;
  
  while (activeTaskUpdates > 0 && waitTime < maxWaitTime) {
    console.log(`[Main] ⏳ 等待 ${activeTaskUpdates} 个任务状态更新完成...`);
    await new Promise(resolve => setTimeout(resolve, checkInterval));
    waitTime += checkInterval;
  }
  
  if (activeTaskUpdates > 0) {
    console.log(`[Main] ⚠️  超时退出，仍有 ${activeTaskUpdates} 个任务更新未完成`);
  } else {
    console.log('[Main] ✅ 所有任务状态更新已完成');
  }
  
  // V3.0 AI+MCP系统已移除，无需清理
  
  if (browserManager) {
    try {
      await browserManager.close();
    } catch (error) {
      console.error('[Main] 清理browserManager失败:', error);
    }
  }
  
  console.log('[Main] 🚀 优雅退出完成，进程即将终止');
  
  // 只在开发环境下通知父进程
  if (process.env.NODE_ENV === 'development') {
    try {
      process.kill(-process.pid, 'SIGTERM');
         } catch (error: any) {
       console.log('[Main] 通知父进程失败（正常）:', error.message);
     }
  }
  
  // 最终退出
  process.exit(0);
};

app.on('before-quit', () => {
  console.log('[Main] before-quit触发 - 启动优雅退出流程');
  if (!isQuitting) {
    isQuitting = true;
    gracefulExit();
  }
});

app.on('will-quit', () => {
  console.log('[Main] will-quit触发 - 备用退出流程');
  if (!isQuitting) {
    isQuitting = true;
    gracefulExit();
  }
});

// 窗口关闭时的处理
app.on('window-all-closed', () => {
  console.log('[Main] 所有窗口已关闭');

  // 🔧 修复：检查是否有正在执行的任务，如果有则不退出应用
  const hasActiveTasks = activeTaskUpdates > 0;
  if (hasActiveTasks) {
    console.log('[Main] 🚫 检测到活跃任务，阻止应用退出以保护任务执行');
    return;
  }

  // 在macOS上，除非用户明确退出，否则应用应该保持活跃
  if (process.platform !== 'darwin') {
    console.log('[Main] 非macOS平台 - 启动优雅退出');
    if (!isQuitting) {
      isQuitting = true;
      gracefulExit();
    }
  } else {
    console.log('[Main] macOS平台 - 保持应用活跃');
  }
});

// 处理未捕获的异常
// ==================== IPC 处理器注册 ====================
// 🔧 修复：将所有IPC处理器移到应用初始化之前注册，避免竞态条件

// IPC handler to get all tasks
ipcMain.handle('get-tasks', async () => {
  return await localStorage.getAllTasks();
});

// IPC handler to get a single task by ID
ipcMain.handle('get-task-by-id', async (event, taskId) => {
  return await localStorage.getTask(taskId);
});

// 🔥 IPC handler for intelligent file parsing
ipcMain.handle('parse-file-for-keywords', async (event, filePath: string) => {
  try {
    console.log(`[Main] 🔍 开始解析文件: ${filePath}`);
    const keywords = await parseFileForKeywords(filePath);
    console.log(`[Main] ✅ 文件解析成功，提取${keywords.length}个关键词`);
    return keywords;
  } catch (error) {
    console.error(`[Main] ❌ 文件解析失败:`, error);
    throw error; // 让前端处理错误
  }
});

// ==================== EnhancedTaskMonitor 真实数据 API ====================

// IPC handler to get worker statuses
ipcMain.handle('get-worker-statuses', async (event, taskId?: string) => {
  try {
    // V2.5简化架构 - 模拟Worker状态
    const workerStatuses = [];
    const platforms = ['taobao', 'xiaohongshu'];

    for (const platform of platforms) {
      const workerId = `${platform}-worker-${Date.now()}`;
      const isHealthy = browserManager ? true : false;

      workerStatuses.push({
        id: workerId,
        platform,
        isHealthy,
        isAvailable: isHealthy,
        successCount: 0,
        failureCount: 0,
        successRate: '100%',
        uptime: Date.now() - (app.getAppMetrics()[0]?.creationTime || Date.now()),
        lastActivity: Date.now()
      });
    }

    return workerStatuses;

    // 如果 AI+MCP 系统未初始化，返回空数组
    return [];
  } catch (error) {
    console.error('[Main] Failed to get worker statuses:', error);
    return [];
  }
});

// IPC handler to get system status
ipcMain.handle('get-system-status', async () => {
  try {
    const os = require('os');

    // 获取真实的系统状态
    const totalMemory = os.totalmem();
    const freeMemory = os.freemem();
    const memoryUsage = Math.round(((totalMemory - freeMemory) / totalMemory) * 100);

    // 获取 CPU 使用率（简化版）
    const cpus = os.cpus();
    let totalIdle = 0;
    let totalTick = 0;

    cpus.forEach((cpu: any) => {
      for (const type in cpu.times) {
        totalTick += cpu.times[type];
      }
      totalIdle += cpu.times.idle;
    });

    const cpuUsage = Math.round(100 - (totalIdle / totalTick) * 100);

    return {
      isOnline: true,
      lastHeartbeat: new Date(),
      activeConnections: browserManager ? 2 : 0, // 淘宝 + 小红书
      memoryUsage,
      cpuUsage,
      diskUsage: 0 // 简化处理，可以后续添加真实的磁盘使用率
    };
  } catch (error) {
    console.error('[Main] Failed to get system status:', error);
    return {
      isOnline: false,
      lastHeartbeat: new Date(),
      activeConnections: 0,
      memoryUsage: 0,
      cpuUsage: 0,
      diskUsage: 0
    };
  }
});

// IPC handler to get real-time statistics
ipcMain.handle('get-realtime-stats', async () => {
  try {
    // V2.5简化架构 - 基础统计信息
    const tasks = await localStorage.getAllTasks();
    const completedTasks = tasks.filter(t => t.status === 'COMPLETED').length;
    const failedTasks = tasks.filter(t => t.status === 'FAILED').length;
    const totalTasks = completedTasks + failedTasks;

    return {
      tasksProcessed: totalTasks,
      successRate: totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 100,
      avgResponseTime: 2500, // 平均响应时间（毫秒）
      errorsInLastHour: failedTasks,
      activeWorkers: browserManager ? 2 : 0, // 淘宝 + 小红书
      queueLength: tasks.filter(t => ['PENDING', 'DISCOVERING', 'SCRAPING', 'ANALYZING'].includes(t.status)).length
    };
  } catch (error) {
    console.error('[Main] Failed to get real-time stats:', error);
    return {
      tasksProcessed: 0,
      successRate: 0,
      avgResponseTime: 0,
      errorsInLastHour: 0,
      activeWorkers: 0,
      queueLength: 0
    };
  }
});

// IPC handler to control task
ipcMain.handle('control-task', async (event, taskId: string, action: 'stop') => {
  try {
    console.log(`[Main] Controlling task ${taskId}: ${action}`);

    const task = await localStorage.getTask(taskId);
    if (!task) {
      throw new Error(`Task ${taskId} not found`);
    }

    switch (action) {
      case 'stop':
        await updateTaskWithTracking(taskId, {
          status: 'CANCELLED',
          progress: { message: '任务已停止' }
        });
        break;
    }

    console.log(`[Main] Task ${taskId} ${action} completed`);
  } catch (error) {
    console.error(`[Main] Failed to ${action} task ${taskId}:`, error);
    throw error;
  }
});

// IPC handler to restart worker
ipcMain.handle('restart-worker', async (event, workerId: string) => {
  try {
    console.log(`[Main] Restarting worker: ${workerId}`);

    // 模拟重启过程
    await new Promise(resolve => setTimeout(resolve, 2000));

    console.log(`[Main] Worker ${workerId} restarted successfully`);
  } catch (error) {
    console.error(`[Main] Failed to restart worker ${workerId}:`, error);
    throw error;
  }
});

// 🔥 真正的数据导出处理器 - 使用ExportManager
ipcMain.handle('export-task-data', async (event, taskId: string, dataType: 'links' | 'comments' | 'insights' | 'all', format: 'excel' | 'csv' | 'json') => {
  try {
    console.log(`[Main] 🚀 Starting REAL ${format.toUpperCase()} export: ${taskId}, type: ${dataType}`);

    const task = await localStorage.getTask(taskId);
    if (!task) {
      throw new Error(`Task ${taskId} not found`);
    }

    // 🔥 数据类型映射：前端传递的'links'映射为'products'
    const dataTypeMapping: Record<string, 'products' | 'comments' | 'insights' | 'all'> = {
      'links': 'products',
      'comments': 'comments',
      'insights': 'insights',
      'all': 'all'
    };

    const mappedDataType = dataTypeMapping[dataType] || 'products';
    console.log(`[Main] 🔄 Mapped dataType: ${dataType} → ${mappedDataType}`);

    // 🔥 使用真正的导出管理器
    const { ExportManager } = await import('../lib/export-manager');

    const exportOptions = {
      format,
      dataType: mappedDataType,
      includeMetadata: true
    };

    const result = await ExportManager.exportTaskData(task, exportOptions);

    if (!result.success) {
      throw new Error(result.error || 'Export failed');
    }

    console.log(`[Main] ✅ REAL ${format.toUpperCase()} export completed successfully!`);
    console.log(`[Main] 📁 File: ${result.fileName} (${result.fileSize} bytes)`);
    console.log(`[Main] 📊 Records: ${result.recordCount}`);

    // 返回详细的导出结果
    return {
      filePath: result.filePath,
      fileName: result.fileName,
      fileSize: result.fileSize,
      recordCount: result.recordCount,
      exportTime: result.exportTime,
      format: format.toUpperCase(),
      dataType: mappedDataType
    };
  } catch (error) {
    console.error(`[Main] ❌ REAL export failed:`, error);
    throw error;
  }
});

process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);

  // 🔧 修复：检查是否为浏览器相关的异常，避免误杀应用
  const errorMessage = error.message || '';
  const isBrowserRelated = errorMessage.includes('browser') ||
                          errorMessage.includes('chromium') ||
                          errorMessage.includes('playwright') ||
                          errorMessage.includes('Target closed') ||
                          errorMessage.includes('Protocol error');

  if (isBrowserRelated) {
    console.warn('[Main] 🔧 Browser-related exception detected, not exiting application:', errorMessage);
    return;
  }

  // 🔧 修复：检查是否有活跃任务，如果有则不退出
  if (activeTaskUpdates > 0) {
    console.warn('[Main] 🚫 Exception occurred but active tasks detected, not exiting application');
    return;
  }

  console.error('[Main] 💥 Fatal exception, exiting application');
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);

  // 🔧 修复：检查是否为浏览器相关的Promise rejection
  const reasonStr = String(reason);
  const isBrowserRelated = reasonStr.includes('browser') ||
                          reasonStr.includes('chromium') ||
                          reasonStr.includes('playwright') ||
                          reasonStr.includes('Target closed') ||
                          reasonStr.includes('Protocol error');

  if (isBrowserRelated) {
    console.warn('[Main] 🔧 Browser-related rejection detected, not exiting application:', reasonStr);
    return;
  }

  // 🔧 修复：检查是否有活跃任务，如果有则不退出
  if (activeTaskUpdates > 0) {
    console.warn('[Main] 🚫 Promise rejection occurred but active tasks detected, not exiting application');
    return;
  }

  console.error('[Main] 💥 Fatal promise rejection, exiting application');
  process.exit(1);
});