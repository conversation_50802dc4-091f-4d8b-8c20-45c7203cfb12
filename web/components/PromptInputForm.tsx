"use client"

import React, { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON><PERSON>, ArrowRight, Loader2, ChevronDown, Sparkles, X, CheckCircle } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { useToast } from "@/hooks/use-toast"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Tooltip, TooltipProvider, TooltipTrigger, TooltipContent } from "@/components/ui/tooltip"
import { Task } from "@/types/task"

export function PromptInputForm() {
  const router = useRouter()
  const [scanMode, setScanMode] = useState<"deep" | "quick">("deep")
  const [keywords, setKeywords] = useState("")
  const [filePath, setFilePath] = useState<string | null>(null)
  const [fileName, setFileName] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [fileKeywords, setFileKeywords] = useState<string[]>([]) // 文件解析出的关键词预览
  const [isParsingFile, setIsParsingFile] = useState(false) // 文件解析状态

  const { toast } = useToast()

  useEffect(() => {
    if (scanMode === 'quick' && filePath) {
      setFilePath(null)
      setFileName(null)
      toast({
        title: "模式切换提示",
        description: "“快速扫描”模式不支持文件上传，已为您移除所选文件。",
      })
    }
  }, [scanMode, filePath, toast])

  const parseKeywords = (text: string): string[] => {
    // 智能解析策略：
    // 1. 优先使用明确的分隔符（逗号、顿号）
    // 2. 如果没有明确分隔符，使用换行分隔
    // 3. 最后才考虑空格分隔，但要智能处理

    const hasCommaOrDot = /[、,，]/.test(text);
    const hasNewline = /[\n\r]/.test(text);

    if (hasCommaOrDot) {
      // 有逗号或顿号时，只用这些分隔，保留关键词内部的空格
      return text
        .split(/[、,，]+/)
        .map((k) => k.trim())
        .filter((k) => k.length > 0);
    } else if (hasNewline) {
      // 有换行时，用换行分隔，保留关键词内部的空格
      return text
        .split(/[\n\r]+/)
        .map((k) => k.trim())
        .filter((k) => k.length > 0);
    } else {
      // 没有明确分隔符时，智能处理空格
      // 如果整个文本看起来像单个产品名称，不分割
      const trimmed = text.trim();
      if (trimmed.length === 0) {
        // 空字符串
        return [];
      } else if (trimmed.length <= 20 && !/\s{2,}/.test(trimmed)) {
        // 短文本且没有多个连续空格，可能是单个关键词
        return [trimmed];
      } else {
        // 长文本或有多个连续空格，按空格分割
        return trimmed
          .split(/\s+/)
          .filter((k) => k.length > 0);
      }
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

    const hasKeywords = keywords.trim().length > 0
    const hasFile = !!filePath

    if (!hasKeywords && !hasFile) {
      toast({ title: "请输入关键词或上传文件", variant: "destructive" })
      setIsLoading(false)
      return
    }

    const taskData: Partial<Task> = {
      scanMode,
      initialKeywords: hasFile ? [filePath] : parseKeywords(keywords),
      source: hasFile ? 'file' : 'manual',
    }

    try {
      const newTask = await window.electronAPI.startTask(taskData)
      console.log(`[PromptInputForm] Task created successfully:`, newTask)

      toast({
        title: "任务创建成功",
        description: `已开始${scanMode === "deep" ? "深度" : "快速"}扫描分析`,
      })

      // 🔧 修复：确保任务ID一致性，优先使用id字段
      if (newTask && newTask.id) {
        const taskId = newTask.id
        console.log(`[PromptInputForm] Task ID: ${taskId}, navigating to dashboard/${taskId}`)

        // 🔧 修复：添加短暂延迟，确保任务状态已经同步到前端
        setTimeout(() => {
          router.push(`/dashboard/${taskId}`)
        }, 200)
      } else {
        console.error(`[PromptInputForm] Invalid task response - missing id:`, newTask)
        toast({
          title: "任务创建异常",
          description: "任务已创建但无法获取任务ID",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error(error)
      toast({
        title: "创建任务失败",
        description: error instanceof Error ? error.message : "请查看控制台获取更多信息",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // 清理文件相关状态
  const clearFile = () => {
    setFilePath(null)
    setFileName(null)
    setFileKeywords([])
    setIsParsingFile(false)
  }

  // 文件上传处理函数 - 支持智能关键词解析
  const handleFileUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0]
    if (selectedFile) {
       // In Electron, we get the path property.
      if ('path' in selectedFile) {
        setFilePath(selectedFile.path)
        setFileName(selectedFile.name)
        setKeywords('') // Clear keywords when a file is uploaded to avoid confusion

        // 🔥 智能解析文件中的关键词
        setIsParsingFile(true)
        try {
          console.log('[PromptInputForm] 🔍 开始解析文件关键词...')
          const parsedKeywords = await window.electronAPI?.parseFileForKeywords?.(selectedFile.path) || []
          setFileKeywords(parsedKeywords)

          console.log(`[PromptInputForm] ✅ 成功解析${parsedKeywords.length}个关键词:`, parsedKeywords.slice(0, 5))

          if (parsedKeywords.length === 0) {
            toast({
              title: "文件解析完成",
              description: "未在文件中找到有效的关键词，请检查文件格式。",
              variant: "destructive",
            })
          } else {
            toast({
              title: "文件解析成功",
              description: `智能识别到 ${parsedKeywords.length} 个关键词`,
            })
          }
        } catch (error) {
          console.error('[PromptInputForm] ❌ 文件解析失败:', error)
          toast({
            title: "文件解析失败",
            description: error instanceof Error ? error.message : "请检查文件格式是否正确",
            variant: "destructive",
          })
          clearFile() // 解析失败时清理状态
        } finally {
          setIsParsingFile(false)
        }
      } else {
        toast({
          title: "文件选择错误",
          description: "无法获取文件路径，请重试。",
          variant: "destructive",
        })
      }
    }
    // Reset the input value to allow re-uploading the same file
    e.target.value = ''
  }

  const getScanModeDescription = (mode: "quick" | "deep") => {
    if (mode === "quick") {
      return "直接使用您的关键词进行分析，快速获得结果（消耗1点数）"
    }
    return "AI智能扩展和优化关键词，提供更全面深入的分析（消耗2点数）"
  }

  return (
    <div className="w-full mb-6">
      <form onSubmit={handleSubmit}>
        <div className="border-[0.5px] border-gray-200 w-full p-6 shadow-sm relative bg-white rounded-2xl min-h-[120px]">
        <input
            type="file"
            id="file-upload-input"
            accept=".csv,.xlsx,.xls"
            onChange={handleFileUpload}
            className="hidden"
            disabled={scanMode === 'quick'}
          />
          <div className="pb-12">
            {/* 编辑器风格的输入区域 */}
            <div className="min-h-6 relative">
              <textarea
                value={keywords}
                onChange={(e) => setKeywords(e.target.value)}
                placeholder={filePath ? "已选择文件，将忽略此处的手动输入" : "请输入关键词，如“连衣裙”, “夏季连衣裙”, “职业连衣裙”"}
                className="w-full min-h-6 resize-none border-none outline-none focus:outline-none bg-transparent text-gray-900 placeholder-gray-500 leading-relaxed"
                style={{ 
                  lineHeight: '1.5',
                  fontFamily: 'system-ui, -apple-system, sans-serif'
                }}
                rows={1}
                onInput={(e) => {
                  const target = e.target as HTMLTextAreaElement
                  target.style.height = 'auto'
                  target.style.height = Math.max(24, target.scrollHeight) + 'px'
                }}
                disabled={!!filePath}
              />
            </div>
             {filePath && (
              <div className="mt-2 space-y-2">
                {/* 文件信息显示 */}
                <div className="text-sm text-gray-600 bg-amber-50 p-2 rounded-lg border border-amber-200 flex items-center justify-between gap-2">
                  <div className="flex items-center gap-2 truncate">
                    <Paperclip size={14} className="text-amber-600 flex-shrink-0" />
                    <span className="truncate" title={fileName || ''}>📄 {fileName || "未知文件"}</span>
                    {isParsingFile && (
                      <Loader2 className="w-4 h-4 animate-spin text-amber-600" />
                    )}
                  </div>
                  <Button type="button" variant="ghost" size="icon" className="h-6 w-6 rounded-full" onClick={clearFile}>
                    <X size={14} />
                  </Button>
                </div>

                {/* 关键词预览 */}
                {fileKeywords.length > 0 && (
                  <div className="bg-green-50 p-3 rounded-lg border border-green-200">
                    <div className="flex items-center gap-2 mb-2">
                      <CheckCircle className="w-4 h-4 text-green-600" />
                      <span className="text-sm font-medium text-green-700">
                        智能识别到 {fileKeywords.length} 个关键词
                      </span>
                    </div>
                    <div className="flex flex-wrap gap-1 max-h-20 overflow-y-auto">
                      {fileKeywords.slice(0, 20).map((keyword, index) => (
                        <span
                          key={index}
                          className="inline-block px-2 py-1 text-xs bg-white text-green-700 rounded border border-green-300"
                        >
                          {keyword}
                        </span>
                      ))}
                      {fileKeywords.length > 20 && (
                        <span className="inline-block px-2 py-1 text-xs text-green-600 italic">
                          +{fileKeywords.length - 20} 更多...
                        </span>
                      )}
                    </div>
                  </div>
                )}

                {/* 解析状态提示 */}
                {isParsingFile && (
                  <div className="bg-blue-50 p-2 rounded-lg border border-blue-200">
                    <div className="flex items-center gap-2">
                      <Loader2 className="w-4 h-4 animate-spin text-blue-600" />
                      <span className="text-sm text-blue-700">正在智能解析文件中的关键词...</span>
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>

          {/* 底部功能按钮栏 */}
          <div className="absolute bottom-4 left-6 right-6 flex items-center justify-between">
            <div className="flex items-center space-x-2">
              {/* 附件按钮 */}
              <TooltipProvider delayDuration={200}>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div className="inline-block">
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => document.getElementById('file-upload-input')?.click()}
                        disabled={scanMode === 'quick'}
                        className="h-8 px-3 text-xs border-amber-200 hover:bg-amber-50 hover:border-amber-300 rounded-full flex items-center gap-1 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        <Paperclip className="w-4 h-4 text-amber-600" />
                        <span className="font-medium text-amber-700">附件</span>
                      </Button>
                    </div>
                  </TooltipTrigger>
                  <TooltipContent side="top" align="start" className="max-w-xs">
                    {scanMode === 'quick' ? (
                      <p>上传关键词表只能用于深度扫描模式</p>
                    ) : (
                      <div className="space-y-1">
                        <p className="font-medium">支持 Excel/CSV 文件上传</p>
                      </div>
                    )}
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>

              {/* 模式选择按钮 */}
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    className="h-8 px-3 text-xs border-amber-200 hover:bg-amber-50 hover:border-amber-300 rounded-full flex items-center gap-1"
                  >
                    <Sparkles className="w-4 h-4 text-amber-600" />
                    <span className="font-medium text-amber-700">{scanMode === "deep" ? "深度" : "快速"}</span>
                    <ChevronDown className="w-3 h-3 text-amber-600" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-80 p-4" align="start">
                  <div className="space-y-3">
                    <h4 className="font-medium text-sm">扫描模式</h4>
                    <RadioGroup
                      value={scanMode}
                      onValueChange={(value: "quick" | "deep") => setScanMode(value)}
                      className="space-y-3"
                    >
                      <Label
                        htmlFor="deep-popup"
                        className="flex items-start space-x-3 p-3 border border-amber-200 rounded-lg hover:bg-amber-50 cursor-pointer"
                      >
                        <RadioGroupItem value="deep" id="deep-popup" className="mt-1" />
                        <div className="flex-1">
                          <div className="font-medium text-sm">
                            深度分析 <span className="text-amber-600">(推荐)</span>
                          </div>
                          <p className="text-xs text-gray-600 mt-1">{getScanModeDescription("deep")}</p>
                        </div>
                      </Label>
                      <Label
                        htmlFor="quick-popup"
                        className="flex items-start space-x-3 p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer"
                      >
                        <RadioGroupItem value="quick" id="quick-popup" className="mt-1" />
                        <div className="flex-1">
                          <div className="font-medium text-sm">
                            快速扫描
                          </div>
                          <p className="text-xs text-gray-600 mt-1">{getScanModeDescription("quick")}</p>
                        </div>
                      </Label>
                    </RadioGroup>
                  </div>
                </PopoverContent>
              </Popover>
            </div>

            {/* 提交按钮 */}
            <Button
              type="submit"
              disabled={isLoading || (!keywords.trim() && !filePath)}
              className="h-8 w-12 rounded-full bg-amber-500 hover:bg-amber-600 disabled:bg-gray-300 disabled:cursor-not-allowed text-white p-0 flex items-center justify-center"
            >
              {isLoading ? (
                <Loader2 className="w-5 h-5 animate-spin text-white" />
              ) : (
                <ArrowRight className="w-5 h-5 text-white" />
              )}
            </Button>
          </div>
        </div>
      </form>
    </div>
  )
}
